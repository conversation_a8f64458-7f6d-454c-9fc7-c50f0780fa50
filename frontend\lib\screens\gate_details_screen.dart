import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/database_service.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:firebase_core/firebase_core.dart';
import 'gate_schedule_screen.dart';
import 'farmers_screen.dart';
import 'dart:async';

class GateDetailsScreen extends StatefulWidget {
  final String gateId;
  final String gateName;

  const GateDetailsScreen({
    Key? key,
    required this.gateId,
    required this.gateName,
  }) : super(key: key);

  @override
  State<GateDetailsScreen> createState() => GateDetailsScreenState();
}

class GateDetailsScreenState extends State<GateDetailsScreen> {
  late DatabaseService _databaseService;
  bool _isLoading = true;
  bool _isRequestingLocation = false;
  Map<String, dynamic> _gateData = {};
  List<Map<String, dynamic>> _assignedFarmers = [];
  bool _loadingFarmers = true;

  // Button state management
  bool _isCommandPending = false;
  String? _pendingCommandId;
  Timer? _commandTimeoutTimer;
  StreamSubscription? _commandStatusSubscription;
  static const int _commandTimeoutSeconds = 120; // 2 minutes to match server timeout

  // Add static tracking for all gates to prevent multiple commands
  static Map<String, bool> _globalGateCommandPending = {};
  static Map<String, String?> _globalGatePendingCommandIds = {};

  @override
  void initState() {
    super.initState();
    _databaseService = Provider.of<DatabaseService>(context, listen: false);
    _loadGateData();
    _loadAssignedFarmers();
  }

  @override
  void dispose() {
    _commandTimeoutTimer?.cancel();
    _commandStatusSubscription?.cancel();
    super.dispose();
  }

  /// Start command pending state and set up timeout
  void _startCommandPending(String commandId) {
    setState(() {
      _isCommandPending = true;
      _pendingCommandId = commandId;
    });

    // Set up timeout timer
    _commandTimeoutTimer?.cancel();
    _commandTimeoutTimer = Timer(Duration(seconds: _commandTimeoutSeconds), () {
      if (_isCommandPending && _pendingCommandId == commandId) {
        _handleCommandTimeout();
      }
    });

    // Listen for command status changes or gate status changes
    _startListeningForCommandResponse(commandId);
  }

  /// Handle command timeout
  void _handleCommandTimeout() {
    setState(() {
      _isCommandPending = false;
      _pendingCommandId = null;
    });

    _commandTimeoutTimer?.cancel();
    _commandStatusSubscription?.cancel();

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('⏰ Command timeout - No response from gate'),
          backgroundColor: Colors.orange,
          duration: Duration(seconds: 5),
        ),
      );
    }
  }

  /// Handle successful command response
  void _handleCommandSuccess() {
    setState(() {
      _isCommandPending = false;
      _pendingCommandId = null;
    });

    _commandTimeoutTimer?.cancel();
    _commandStatusSubscription?.cancel();

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('✅ Gate responded successfully!'),
          backgroundColor: Colors.green,
          duration: Duration(seconds: 3),
        ),
      );
    }
  }

  /// Listen for command response (gate status changes)
  void _startListeningForCommandResponse(String commandId) {
    _commandStatusSubscription?.cancel();

    // Listen to gate data changes to detect when gate responds
    _commandStatusSubscription = _databaseService.listenToGateData(widget.gateId).listen((event) {
      if (event.snapshot.exists && _isCommandPending && _pendingCommandId == commandId) {
        final data = Map<String, dynamic>.from(event.snapshot.value as Map);
        final lastOperation = data['lastOperation'];
        final operationStatus = data['operationStatus'];

        // Check if this is a response to our command
        if (lastOperation != null && operationStatus == 'completed') {
          _handleCommandSuccess();
        } else if (operationStatus == 'failed' || operationStatus == 'timeout') {
          _handleCommandTimeout();
        }
      }
    });
  }

  Future<void> _loadGateData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final snapshot = await FirebaseDatabase.instance
          .ref()
          .child('farmflow')
          .child('gates')
          .child(widget.gateId)
          .get();

      if (snapshot.exists) {
        setState(() {
          _gateData = Map<String, dynamic>.from(snapshot.value as Map);
          _isLoading = false;
        });
      } else {
        setState(() {
          _gateData = {
            'gateStatus': 'unknown',
            'lastUpdated': DateTime.now().toIso8601String(),
          };
          _isLoading = false;
        });
      }
    } catch (e) {
      print('Error loading gate data: $e');
      setState(() {
        _gateData = {
          'gateStatus': 'error',
          'lastUpdated': DateTime.now().toIso8601String(),
        };
        _isLoading = false;
      });
    }
  }

  Future<void> _loadAssignedFarmers() async {
    setState(() {
      _loadingFarmers = true;
    });

    try {
      final allFarmers = await _databaseService.getAllFarmers();
      final assignedFarmers = allFarmers.where((farmer) =>
        farmer['assignedGateId'] == widget.gateId
      ).toList();

      // Sort farmers by name
      assignedFarmers.sort((a, b) =>
        (a['name'] ?? '').compareTo(b['name'] ?? '')
      );

      setState(() {
        _assignedFarmers = assignedFarmers;
        _loadingFarmers = false;
      });

      print('Loaded ${assignedFarmers.length} farmers assigned to gate ${widget.gateId}');
    } catch (e) {
      print('Error loading assigned farmers: $e');
      setState(() {
        _assignedFarmers = [];
        _loadingFarmers = false;
      });
    }
  }

  /// Send command to Orange Pi server via Firebase
  Future<void> _sendGateCommand(String gateId, String action) async {
    print('🚀 Starting _sendGateCommand: $action for gate $gateId');

    try {
      // Get gate phone number from database
      final gateData = await _databaseService.getGateData(gateId);
      final phoneNumber = gateData?['phoneNumber'];

      if (phoneNumber == null) {
        print('⚠️ Warning: No phone number found for gate $gateId');
      } else {
        print('📱 Found phone number for gate $gateId: $phoneNumber');
      }

      // Generate unique command ID
      final commandId = 'cmd_${gateId}_${action.toLowerCase()}_${DateTime.now().millisecondsSinceEpoch}';
      print('🆔 Generated command ID: $commandId');

      // Create command data na expected sa server
      final commandData = {
        'gateId': gateId,
        'action': action.toUpperCase(), // OPEN or CLOSE
        'timestamp': DateTime.now().toIso8601String(),
        'source': 'app',
        'status': 'pending',
        'userId': 'app_user',
        'commandType': 'manual',
        'createdAt': DateTime.now().toIso8601String(),
        'phoneNumber': phoneNumber, // Include phone number for Orange Pi
      };

      print('📋 Command data: $commandData');
      print('📍 Writing to path: farmflow/gate_commands/$commandId');

      // Write command to Firebase sa path na gi-monitor sa server
      final database = FirebaseDatabase.instanceFor(
        app: Firebase.app(),
        databaseURL: 'https://farmflow-a2716-default-rtdb.asia-southeast1.firebasedatabase.app/'
      );
      final databaseRef = database.ref('farmflow/gate_commands/$commandId');
      await databaseRef.set(commandData);

      print('✅ Command successfully written to Firebase!');
      print('✅ Command sent to server: $commandId');
      print('📤 Action: $action Gate: $gateId');

      // Verify na na-write jud
      final verification = await databaseRef.get();
      if (verification.exists) {
        print('✅ VERIFIED: Command exists in database');
        print('📊 Stored data: ${verification.value}');
      } else {
        print('❌ WARNING: Command not found in database after writing');
      }

    } catch (e, stackTrace) {
      print('❌ Failed to send command to server: $e');
      print('📍 Stack trace: $stackTrace');

      // Show error sa user
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error sending command: $e'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 5),
          ),
        );
      }

      rethrow;
    }
  }

  /// Simple test method - ONLY sends command, no other database updates
  Future<void> _testSendCommand(String action) async {
    print('🧪 TEST: Sending $action command for gate ${widget.gateId}');

    try {
      await _sendGateCommand(widget.gateId, action);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('🧪 TEST: $action command sent! Check farmflow/gate_commands'),
            duration: Duration(seconds: 5),
            backgroundColor: Colors.blue,
          ),
        );
      }
    } catch (e) {
      print('🧪 TEST FAILED: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('🧪 TEST FAILED: $e'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 5),
          ),
        );
      }
    }
  }

  Future<void> _toggleGate() async {
    // Check if this specific gate already has a pending command globally
    if (_globalGateCommandPending[widget.gateId] == true) {
      print('⚠️ Gate ${widget.gateId} already has a pending command globally, ignoring click');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('⚠️ Gate ${widget.gateId} is already processing a command. Please wait...'),
            backgroundColor: Colors.orange,
            duration: Duration(seconds: 2),
          ),
        );
      }
      return;
    }

    if (_isCommandPending) {
      print('⚠️ Gate ${widget.gateId} already has a local pending command, ignoring click');
      return;
    }

    try {
      final currentStatus = _gateData['gateStatus'] == 'open';
      final action = currentStatus ? 'close' : 'open';

      print('🎯 Gate Details: Toggle gate ${widget.gateId} to $action');

      // Check for existing pending commands in database
      final hasExistingCommand = await _checkForExistingPendingCommand(widget.gateId, action);
      if (hasExistingCommand) {
        print('⚠️ Database already has pending $action command for gate ${widget.gateId}');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('⚠️ Gate ${widget.gateId} already has a pending $action command'),
              backgroundColor: Colors.orange,
              duration: Duration(seconds: 3),
            ),
          );
        }
        return;
      }

      // Generate command ID
      final commandId = 'cmd_${widget.gateId}_${action.toLowerCase()}_${DateTime.now().millisecondsSinceEpoch}';

      // Start pending state for this specific gate globally and locally
      setState(() {
        _isCommandPending = true;
        _pendingCommandId = commandId;
      });
      
      _globalGateCommandPending[widget.gateId] = true;
      _globalGatePendingCommandIds[widget.gateId] = commandId;

      // Start listening for server response through database
      _startListeningForOrangePiResponse();

      // Send command to database (server will pick it up)
      await _sendGateCommand(widget.gateId, action.toUpperCase());

      print('✅ Command sent to database, waiting for server to process...');

    } catch (e) {
      print('❌ Error sending command: $e');
      setState(() {
        _isCommandPending = false;
        _pendingCommandId = null;
      });
      
      _globalGateCommandPending[widget.gateId] = false;
      _globalGatePendingCommandIds[widget.gateId] = null;
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Failed to send command: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<bool> _checkForExistingPendingCommand(String gateId, String action) async {
    try {
      final database = FirebaseDatabase.instanceFor(
        app: Firebase.app(),
        databaseURL: 'https://farmflow-a2716-default-rtdb.asia-southeast1.firebasedatabase.app/'
      );
      
      final commandsRef = database.ref('farmflow/gate_commands');
      final snapshot = await commandsRef.get();
      
      if (snapshot.exists) {
        final commands = Map<String, dynamic>.from(snapshot.value as Map);
        
        for (var entry in commands.entries) {
          final commandData = Map<String, dynamic>.from(entry.value as Map);
          final commandGateId = commandData['gateId'];
          final commandAction = commandData['action'];
          final commandStatus = commandData['status'];
          
          if (commandGateId == gateId && 
              commandAction == action.toUpperCase() && 
              commandStatus == 'pending') {
            print('⚠️ Found existing pending $action command for gate $gateId: ${entry.key}');
            return true;
          }
        }
      }
      
      return false;
    } catch (e) {
      print('❌ Error checking for existing commands: $e');
      return false;
    }
  }

  void _startListeningForOrangePiResponse() {
    _commandStatusSubscription?.cancel();
    
    _commandStatusSubscription = _databaseService.listenToGateData(widget.gateId).listen((event) {
      if (event.snapshot.exists) {
        final data = Map<String, dynamic>.from(event.snapshot.value as Map);
        final operationStatus = data['operationStatus'] ?? '';
        final gateStatus = data['gateStatus'] ?? '';
        final confirmationReceived = data['confirmationReceived'] ?? false;
        final serverMessage = data['serverMessage'] ?? '';

        print('📊 Server Database update: status=$gateStatus, operation=$operationStatus, confirmed=$confirmationReceived');

        setState(() {
          _gateData = data;
        });

        if (_isCommandPending) {
          if (operationStatus == 'completed' && confirmationReceived) {
            // Clear both local and global pending states
            setState(() {
              _isCommandPending = false;
              _pendingCommandId = null;
            });
            
            _globalGateCommandPending[widget.gateId] = false;
            _globalGatePendingCommandIds[widget.gateId] = null;
            
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('✅ Gate ${gateStatus == 'open' ? 'opened' : 'closed'} successfully!'),
                  backgroundColor: Colors.green,
                  duration: Duration(seconds: 3),
                ),
              );
            }
          }
          else if (operationStatus == 'failed') {
            setState(() {
              _isCommandPending = false;
              _pendingCommandId = null;
            });
            
            _globalGateCommandPending[widget.gateId] = false;
            _globalGatePendingCommandIds[widget.gateId] = null;
            
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('❌ ${serverMessage.isNotEmpty ? serverMessage : "Operation failed"}'),
                  backgroundColor: Colors.red,
                  duration: Duration(seconds: 4),
                ),
              );
            }
          }
          else if (operationStatus == 'timeout') {
            setState(() {
              _isCommandPending = false;
              _pendingCommandId = null;
            });
            
            _globalGateCommandPending[widget.gateId] = false;
            _globalGatePendingCommandIds[widget.gateId] = null;
            
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('⏰ Operation timed out'),
                  backgroundColor: Colors.orange,
                  duration: Duration(seconds: 4),
                ),
              );
            }
          }
        }
      }
    });
  }

  void _navigateToScheduleScreen() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => GateScheduleScreen(
          gateId: widget.gateId,
          gateName: widget.gateName,
        ),
      ),
    );
  }

  // Request location from the gate
  Future<void> _requestGateLocation() async {
    try {
      setState(() {
        _isRequestingLocation = true;
      });

      // Show loading message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Requesting location from gate...'),
            duration: Duration(seconds: 2),
          ),
        );
      }

      // Request location from the gate
      await _databaseService.requestGateLocation(widget.gateId);

      // Wait for a moment to allow the Orange Pi to process the request
      await Future.delayed(Duration(seconds: 3));

      // Reload gate data to reflect changes
      await _loadGateData();

      if (mounted) {
        setState(() {
          _isRequestingLocation = false;
        });

        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('📍 Location command sent! Check farmflow/gate_commands in database'),
            backgroundColor: Colors.blue,
            duration: Duration(seconds: 4),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isRequestingLocation = false;
        });

        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error requesting location: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Build location information widget
  Widget _buildLocationInfo() {
    final latitude = _gateData['latitude'];
    final longitude = _gateData['longitude'];
    final lastLocationUpdate = _gateData['lastLocationUpdate'];
    final locationRequestStatus = _gateData['locationRequestStatus'];

    if (_isRequestingLocation) {
      return Center(
        child: Column(
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 8),
            Text('Requesting location...'),
          ],
        ),
      );
    }

    if (latitude != null && longitude != null) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Current Location:'),
          SizedBox(height: 4),
          Text('Latitude: ${_parseCoordinate(latitude, 8.2174).toStringAsFixed(6)}'),
          Text('Longitude: ${_parseCoordinate(longitude, 125.0342).toStringAsFixed(6)}'),
          if (lastLocationUpdate != null) ...[
            SizedBox(height: 4),
            Text(
              'Last Updated: ${_formatDateTime(lastLocationUpdate)}',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
          if (locationRequestStatus == 'pending') ...[
            SizedBox(height: 8),
            Row(
              children: [
                SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
                SizedBox(width: 8),
                Text('Location update pending...', style: TextStyle(fontStyle: FontStyle.italic)),
              ],
            ),
          ],
        ],
      );
    } else {
      return Text('No location data available. Request current location to update.');
    }
  }

  void _showFarmerDetails(Map<String, dynamic> farmer) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(farmer['name'] ?? 'Unknown Farmer'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildFarmerDetailRow('Contact', farmer['contactNumber'] ?? 'N/A'),
            _buildFarmerDetailRow('Address', farmer['address'] ?? 'N/A'),
            if (farmer['email'] != null && farmer['email'].toString().isNotEmpty)
              _buildFarmerDetailRow('Email', farmer['email']),
            if (farmer['notes'] != null && farmer['notes'].toString().isNotEmpty)
              _buildFarmerDetailRow('Notes', farmer['notes']),
            _buildFarmerDetailRow('Status', farmer['isActive'] == true ? 'Active' : 'Inactive'),
            if (farmer['registrationDate'] != null)
              _buildFarmerDetailRow('Registered', _formatDateTime(farmer['registrationDate'])),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _showUnassignConfirmation(farmer);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.orange),
            child: Text('Unassign', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  Widget _buildFarmerDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  void _showUnassignConfirmation(Map<String, dynamic> farmer) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Unassign Farmer'),
        content: Text(
          'Are you sure you want to unassign ${farmer['name']} from ${widget.gateName}?'
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              await _unassignFarmer(farmer['id']);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.orange),
            child: Text('Unassign', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  Future<void> _unassignFarmer(String farmerId) async {
    try {
      await _databaseService.updateFarmerData(farmerId, {
        'assignedGateId': null,
        'assignedGateName': null,
        'lastUpdated': DateTime.now().toIso8601String(),
      });

      // Refresh the assigned farmers list
      await _loadAssignedFarmers();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Farmer unassigned successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error unassigning farmer: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Helper function to safely parse coordinate values
  double _parseCoordinate(dynamic value, double defaultValue) {
    if (value == null) return defaultValue;

    try {
      if (value is double) return value;
      if (value is int) return value.toDouble();
      if (value is String) {
        final parsed = double.tryParse(value);
        return parsed ?? defaultValue;
      }
      return defaultValue;
    } catch (e) {
      print('Error parsing coordinate: $value, using default: $defaultValue');
      return defaultValue;
    }
  }

  // Format date time string
  String _formatDateTime(String dateTimeString) {
    try {
      final dateTime = DateTime.parse(dateTimeString);
      return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
    } catch (e) {
      return dateTimeString;
    }
  }

  @override
  Widget build(BuildContext context) {
    final gateStatus = _gateData['gateStatus'] ?? 'unknown';
    final operationStatus = _gateData['operationStatus'] ?? 'completed';
    final pendingConfirmation = _gateData['pendingConfirmation'] ?? false;
    final timeoutOccurred = _gateData['timeoutOccurred'] ?? false;
    final errorMessage = _gateData['errorMessage'];
    final lastUpdated = _gateData['lastUpdated'] ?? DateTime.now().toIso8601String();
    final DateTime lastUpdatedTime = DateTime.parse(lastUpdated);
    final formattedLastUpdated = '${lastUpdatedTime.year}-${lastUpdatedTime.month.toString().padLeft(2, '0')}-${lastUpdatedTime.day.toString().padLeft(2, '0')} ${lastUpdatedTime.hour.toString().padLeft(2, '0')}:${lastUpdatedTime.minute.toString().padLeft(2, '0')}';

    return Scaffold(
      appBar: AppBar(
        title: Text(widget.gateName),
      ),
      body: _isLoading
          ? Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Card(
                    child: Padding(
                      padding: EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Gate Status',
                            style: TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          SizedBox(height: 16),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Row(
                                children: [
                                  Container(
                                    width: 16,
                                    height: 16,
                                    decoration: BoxDecoration(
                                      color: _getStatusColor(gateStatus, operationStatus),
                                      shape: BoxShape.circle,
                                    ),
                                  ),
                                  SizedBox(width: 8),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          _getDisplayStatus(gateStatus, operationStatus),
                                          style: TextStyle(
                                            fontSize: 18,
                                            fontWeight: FontWeight.bold,
                                            color: _getStatusColor(gateStatus, operationStatus),
                                          ),
                                        ),
                                        if (pendingConfirmation || operationStatus == 'in_progress')
                                          Text(
                                            'Waiting for confirmation...',
                                            style: TextStyle(
                                              fontSize: 12,
                                              color: Colors.orange,
                                              fontStyle: FontStyle.italic,
                                            ),
                                          ),
                                        if (timeoutOccurred)
                                          Text(
                                            'Operation timed out',
                                            style: TextStyle(
                                              fontSize: 12,
                                              color: Colors.red,
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ),
                                        if (errorMessage != null)
                                          Text(
                                            errorMessage,
                                            style: TextStyle(
                                              fontSize: 11,
                                              color: Colors.red.shade700,
                                            ),
                                            maxLines: 2,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                              ElevatedButton(
                                onPressed: (_isOperationInProgress() || _isCommandPending) ? null : _toggleGate,
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: _getButtonColor(gateStatus, operationStatus),
                                  disabledBackgroundColor: Colors.grey,
                                  padding: EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                ),
                                child: _buildButtonContent(gateStatus, operationStatus, pendingConfirmation),
                              ),
                              SizedBox(height: 8),
                              // 🧪 TEST BUTTONS - Para ma-test ang command sending
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                children: [
                                  ElevatedButton(
                                    onPressed: () => _testSendCommand('OPEN'),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.green,
                                      foregroundColor: Colors.white,
                                    ),
                                    child: Text('🧪 TEST OPEN'),
                                  ),
                                  ElevatedButton(
                                    onPressed: () => _testSendCommand('CLOSE'),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.red,
                                      foregroundColor: Colors.white,
                                    ),
                                    child: Text('🧪 TEST CLOSE'),
                                  ),
                                ],
                              ),
                            ],
                          ),
                          SizedBox(height: 8),
                          Text('Last Updated: $formattedLastUpdated'),
                        ],
                      ),
                    ),
                  ),
                  SizedBox(height: 16),
                  Card(
                    child: Padding(
                      padding: EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Gate Schedule',
                            style: TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          SizedBox(height: 16),
                          Text(
                            'Set up automated schedules to open or close this gate at specific times.',
                            style: TextStyle(fontSize: 16),
                          ),
                          SizedBox(height: 16),
                          ElevatedButton.icon(
                            onPressed: _navigateToScheduleScreen,
                            icon: Icon(Icons.schedule),
                            label: Text('Manage Gate Schedule'),
                            style: ElevatedButton.styleFrom(
                              minimumSize: Size(double.infinity, 50),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  SizedBox(height: 16),
                  Card(
                    child: Padding(
                      padding: EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Gate Location',
                            style: TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          SizedBox(height: 16),
                          _buildLocationInfo(),
                          SizedBox(height: 16),
                          ElevatedButton.icon(
                            onPressed: _requestGateLocation,
                            icon: Icon(Icons.location_on),
                            label: Text('Request Current Location'),
                            style: ElevatedButton.styleFrom(
                              minimumSize: Size(double.infinity, 50),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  SizedBox(height: 16),
                  Card(
                    child: Padding(
                      padding: EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Water Flow Data',
                            style: TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          SizedBox(height: 16),
                          StreamBuilder<DatabaseEvent>(
                            stream: _databaseService.listenToGateWaterflowData(widget.gateId),
                            builder: (context, snapshot) {
                              if (snapshot.connectionState == ConnectionState.waiting) {
                                return Center(child: CircularProgressIndicator());
                              }

                              if (!snapshot.hasData || !snapshot.data!.snapshot.exists) {
                                return Center(
                                  child: Column(
                                    children: [
                                      Text('No water flow data available'),
                                      SizedBox(height: 8),
                                      Text(
                                        'Water flow data will appear when received from gate sensors',
                                        style: TextStyle(fontSize: 12, color: Colors.grey),
                                        textAlign: TextAlign.center,
                                      ),
                                    ],
                                  ),
                                );
                              }

                              // Get the latest waterflow data from the database
                              final waterflowData = Map<String, dynamic>.from(snapshot.data!.snapshot.value as Map);

                              // Sort entries by timestamp to get the most recent
                              final entries = waterflowData.entries.toList();
                              entries.sort((a, b) {
                                try {
                                  final aData = Map<String, dynamic>.from(a.value as Map);
                                  final bData = Map<String, dynamic>.from(b.value as Map);

                                  final aTime = DateTime.parse(aData['timestamp'].toString());
                                  final bTime = DateTime.parse(bData['timestamp'].toString());

                                  return bTime.compareTo(aTime); // Most recent first
                                } catch (e) {
                                  return 0;
                                }
                              });

                              // Check if we have any entries
                              if (entries.isEmpty) {
                                return Center(
                                  child: Text('No water flow data records found'),
                                );
                              }

                              // Get the most recent entry
                              final latestEntry = Map<String, dynamic>.from(entries.first.value as Map);
                              final flowRate = (latestEntry['flowRate'] as num?)?.toDouble() ?? 0.0;
                              final status = _gateData['gateStatus'] ?? 'unknown';
                              final timestamp = latestEntry['timestamp'] ?? DateTime.now().toIso8601String();

                              // Format the timestamp
                              String formattedTime;
                              try {
                                final dateTime = DateTime.parse(timestamp.toString());
                                formattedTime = '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}:${dateTime.second.toString().padLeft(2, '0')}';
                              } catch (e) {
                                formattedTime = 'Unknown time';
                              }

                              // Get total water usage if available
                              double totalWater = 0.0;
                              double totalWaterCubicMeters = 0.0;

                              // Check if we have water usage data in the gate data
                              if (_gateData.containsKey('lastWaterUsage')) {
                                totalWater = (_gateData['lastWaterUsage'] as num?)?.toDouble() ?? 0.0;
                                // Check if cubic meters is already available
                                if (_gateData.containsKey('lastWaterUsageCubicMeters')) {
                                  totalWaterCubicMeters = (_gateData['lastWaterUsageCubicMeters'] as num?)?.toDouble() ?? 0.0;
                                } else {
                                  // Calculate if not available
                                  totalWaterCubicMeters = totalWater / 1000.0;
                                }
                              }

                              return Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Current Flow Rate: ${flowRate.toStringAsFixed(2)} L/s',
                                    style: TextStyle(fontSize: 16),
                                  ),
                                  SizedBox(height: 8),
                                  Text(
                                    'Status: ${status.toUpperCase()}',
                                    style: TextStyle(fontSize: 16),
                                  ),
                                  SizedBox(height: 8),
                                  // Display water usage in both liters and cubic meters
                                  Text(
                                    'Last Water Usage: ${totalWater.toStringAsFixed(2)} L (${totalWaterCubicMeters.toStringAsFixed(3)} m³)',
                                    style: TextStyle(fontSize: 16),
                                  ),
                                  SizedBox(height: 8),
                                  Text(
                                    'Last Updated: $formattedTime',
                                    style: TextStyle(fontSize: 12, color: Colors.grey),
                                  ),
                                  SizedBox(height: 8),
                                  Text(
                                    'Data Source: ${flowRate > 0 ? 'Sensor Reading' : 'No Flow Detected'}',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: flowRate > 0 ? Colors.green : Colors.grey,
                                      fontStyle: FontStyle.italic,
                                    ),
                                  ),
                                  SizedBox(height: 16),
                                  ElevatedButton.icon(
                                    onPressed: () {
                                      // Navigate to water flow reports screen
                                    },
                                    icon: Icon(Icons.water_drop),
                                    label: Text('View Water Flow Reports'),
                                    style: ElevatedButton.styleFrom(
                                      minimumSize: Size(double.infinity, 50),
                                    ),
                                  ),
                                ],
                              );
                            },
                          ),
                        ],
                      ),
                    ),
                  ),

                  SizedBox(height: 16),

                  // Assigned Farmers Section
                  Card(
                    child: Padding(
                      padding: EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(Icons.people, color: Colors.blue),
                              SizedBox(width: 8),
                              Text(
                                'Assigned Farmers',
                                style: TextStyle(
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              Spacer(),
                              Container(
                                padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                decoration: BoxDecoration(
                                  color: Colors.blue.shade100,
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Text(
                                  '${_assignedFarmers.length}',
                                  style: TextStyle(
                                    color: Colors.blue.shade800,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 16),
                          _loadingFarmers
                              ? Center(
                                  child: Padding(
                                    padding: EdgeInsets.all(20),
                                    child: CircularProgressIndicator(),
                                  ),
                                )
                              : _assignedFarmers.isEmpty
                                  ? Container(
                                      padding: EdgeInsets.all(20),
                                      decoration: BoxDecoration(
                                        color: Colors.grey.shade100,
                                        borderRadius: BorderRadius.circular(8),
                                        border: Border.all(color: Colors.grey.shade300),
                                      ),
                                      child: Column(
                                        children: [
                                          Icon(
                                            Icons.person_off,
                                            size: 48,
                                            color: Colors.grey,
                                          ),
                                          SizedBox(height: 8),
                                          Text(
                                            'No farmers assigned to this gate',
                                            style: TextStyle(
                                              color: Colors.grey.shade600,
                                              fontSize: 16,
                                            ),
                                          ),
                                          SizedBox(height: 8),
                                          Text(
                                            'Assign farmers to this gate from the Farmers screen',
                                            style: TextStyle(
                                              color: Colors.grey.shade500,
                                              fontSize: 12,
                                            ),
                                            textAlign: TextAlign.center,
                                          ),
                                        ],
                                      ),
                                    )
                                  : Column(
                                      children: _assignedFarmers.map((farmer) {
                                        final isActive = farmer['isActive'] == true;
                                        return Card(
                                          margin: EdgeInsets.only(bottom: 8),
                                          elevation: 2,
                                          child: ListTile(
                                            leading: CircleAvatar(
                                              backgroundColor: isActive ? Colors.green : Colors.grey,
                                              child: Icon(
                                                Icons.person,
                                                color: Colors.white,
                                              ),
                                            ),
                                            title: Text(
                                              farmer['name'] ?? 'Unknown',
                                              style: TextStyle(
                                                fontWeight: FontWeight.bold,
                                                color: isActive ? Colors.black : Colors.grey,
                                              ),
                                            ),
                                            subtitle: Column(
                                              crossAxisAlignment: CrossAxisAlignment.start,
                                              children: [
                                                Text('📱 ${farmer['contactNumber'] ?? 'No contact'}'),
                                                if (farmer['address'] != null)
                                                  Text('🏠 ${farmer['address']}'),
                                                Text(
                                                  'Status: ${isActive ? 'Active' : 'Inactive'}',
                                                  style: TextStyle(
                                                    color: isActive ? Colors.green : Colors.grey,
                                                    fontWeight: FontWeight.w500,
                                                  ),
                                                ),
                                              ],
                                            ),
                                            trailing: Row(
                                              mainAxisSize: MainAxisSize.min,
                                              children: [
                                                IconButton(
                                                  icon: Icon(Icons.info, color: Colors.blue),
                                                  onPressed: () => _showFarmerDetails(farmer),
                                                  tooltip: 'View Details',
                                                ),
                                                IconButton(
                                                  icon: Icon(Icons.remove_circle, color: Colors.orange),
                                                  onPressed: () => _showUnassignConfirmation(farmer),
                                                  tooltip: 'Unassign',
                                                ),
                                              ],
                                            ),
                                            onTap: () => _showFarmerDetails(farmer),
                                          ),
                                        );
                                      }).toList(),
                                    ),
                          if (_assignedFarmers.isNotEmpty) ...[
                            SizedBox(height: 16),
                            Row(
                              children: [
                                Expanded(
                                  child: ElevatedButton.icon(
                                    onPressed: _loadAssignedFarmers,
                                    icon: Icon(Icons.refresh),
                                    label: Text('Refresh Farmers'),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.blue,
                                      foregroundColor: Colors.white,
                                    ),
                                  ),
                                ),
                                SizedBox(width: 8),
                                Expanded(
                                  child: ElevatedButton.icon(
                                    onPressed: () {
                                      Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                          builder: (context) => FarmersScreen(),
                                        ),
                                      ).then((_) {
                                        // Refresh farmers when returning
                                        _loadAssignedFarmers();
                                      });
                                    },
                                    icon: Icon(Icons.assignment_ind),
                                    label: Text('Manage Assignments'),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.green,
                                      foregroundColor: Colors.white,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
    );
  }

  Color _getStatusColor(String gateStatus, String operationStatus) {
    // Handle pending states
    if (gateStatus.startsWith('pending_') || operationStatus == 'in_progress') {
      return Colors.orange;
    }

    // Handle timeout states
    if (operationStatus == 'timeout') {
      return Colors.red.shade700;
    }

    // Handle normal states
    switch (gateStatus) {
      case 'open':
        return Colors.green;
      case 'closed':
      case 'close':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _getDisplayStatus(String gateStatus, String operationStatus) {
    // Handle pending states
    if (gateStatus == 'pending_open') {
      return 'OPENING';
    } else if (gateStatus == 'pending_close') {
      return 'CLOSING';
    }

    // Handle timeout states
    if (operationStatus == 'timeout') {
      return 'TIMEOUT ERROR';
    }

    // Handle normal states
    return gateStatus.toUpperCase();
  }

  bool _isOperationInProgress() {
    final operationStatus = _gateData['operationStatus'] ?? 'completed';
    final pendingConfirmation = _gateData['pendingConfirmation'] ?? false;
    return operationStatus == 'in_progress' || pendingConfirmation;
  }

  Color _getButtonColor(String gateStatus, String operationStatus) {
    // Button disabled during command pending or processing
    if (_isCommandPending || operationStatus == 'in_progress' || operationStatus == 'pending') {
      return Colors.grey;
    }

    if (operationStatus == 'timeout' || operationStatus == 'failed') {
      return Colors.red.shade700;
    }

    // Normal colors based on current status
    return gateStatus == 'open' ? Colors.red : Colors.green;
  }

  Widget _buildButtonContent(String gateStatus, String operationStatus, bool pendingConfirmation) {
    // Show command pending state (waiting for Orange Pi)
    if (_isCommandPending) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
          ),
          SizedBox(width: 8),
          Text(
            'WAITING FOR ORANGE PI...',
            style: TextStyle(color: Colors.white, fontSize: 12),
          ),
        ],
      );
    }

    // Show processing state (Orange Pi is working)
    if (operationStatus == 'in_progress' || operationStatus == 'pending') {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
          ),
          SizedBox(width: 8),
          Text(
            'ORANGE PI PROCESSING...',
            style: TextStyle(color: Colors.white, fontSize: 12),
          ),
        ],
      );
    }

    if (operationStatus == 'timeout') {
      return Text(
        'ORANGE PI TIMEOUT - RETRY',
        style: TextStyle(color: Colors.white, fontSize: 12),
      );
    }

    if (operationStatus == 'failed') {
      return Text(
        'ORANGE PI FAILED - RETRY',
        style: TextStyle(color: Colors.white, fontSize: 12),
      );
    }

    // Normal button text
    return Text(
      gateStatus == 'open' ? 'CLOSE GATE' : 'OPEN GATE',
      style: TextStyle(color: Colors.white),
    );
  }
}









