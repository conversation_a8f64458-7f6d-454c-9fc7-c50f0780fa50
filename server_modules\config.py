#!/usr/bin/env python3
"""
Configuration module for FarmFlow Orange Pi Server
Contains all configuration settings, Firebase config, and constants
"""

import os
import sys
import logging

# === System Configuration ===
# Check if we're running on Orange Pi (or similar Linux system)
IS_LINUX = sys.platform.startswith('linux')
IS_WINDOWS = sys.platform.startswith('win')

# Serial port configuration - auto-detect platform
if IS_WINDOWS:
    # Windows COM ports for USB-to-Serial adapters or SIM800L modules
    SERIAL_PORT = 'COM3'  # Common Windows COM port, adjust as needed
    print(f"[CONFIG] Windows detected - using serial port: {SERIAL_PORT}")
    print(f"[CONFIG] If SIM800L is on different port, update SERIAL_PORT in config.py")
else:
    # Linux/Orange Pi serial port
    SERIAL_PORT = '/dev/ttyS3'  # Default for Orange Pi
    print(f"[CONFIG] Linux detected - using serial port: {SERIAL_PORT}")

SERIAL_BAUD_RATE = 115200
SERIAL_TIMEOUT = 2

# === Firebase Configuration ===
FIREBASE_CONFIG = {
    "apiKey": "AIzaSyBk-LeGSskGSjZa4pVYPr2cS759Hp86Cp4",
    "authDomain": "farmflow-a2716.firebaseapp.com",
    "databaseURL": "https://farmflow-a2716-default-rtdb.asia-southeast1.firebasedatabase.app/",
    "projectId": "farmflow-a2716",
    "storageBucket": "farmflow-a2716.appspot.com",
    "messagingSenderId": "486523327700",
    "appId": "1:486523327700:web:18fdeeeca6d47af8041741",
    "measurementId": "G-RLTBV7J4VP"
}

# === Gate Phone Numbers ===
# Phone numbers are now managed through the app and stored in Firebase
# Remove hardcoded phone numbers - gates register through mobile app
GATE_PHONES = {}

# Initialize empty reverse lookup - will be populated from database
PHONE_TO_GATE = {}

def update_phone_mappings(gate_phones_dict):
    """Update phone mappings from database"""
    global GATE_PHONES, PHONE_TO_GATE
    GATE_PHONES = gate_phones_dict
    PHONE_TO_GATE = {phone: gate_id for gate_id, phone in gate_phones_dict.items()}

# === SMS Configuration ===
SMS_CONFIG = {
    "check_interval": 10,       # Check for SMS every 10 seconds
    "max_retries": 3,           # Maximum retries for SMS operations
    "timeout": 180,             # SMS operation timeout in seconds (3 minutes)
    "delete_after_read": True,  # Delete SMS after processing

    # SMS Sending Timing Configuration
    "at_command_delay": 2.0,    # seconds to wait after AT commands
    "sms_send_delay": 10.0,     # seconds to wait for SMS sending
    "read_buffer_size": 500,    # buffer size for reading responses
    "response_timeout": 120.0,  # seconds to wait for SMS response from gate (2 minutes)
}

# === Gate Operation Configuration ===
GATE_CONFIG = {
    "operation_timeout": 150,   # Timeout for gate operations in seconds (2.5 minutes)
    "max_consecutive_errors": 5,  # Maximum consecutive errors before stopping
    "command_retry_delay": 5,   # Delay between command retries in seconds
    "status_check_interval": 10,  # Check gate status every 10 seconds
}

# === Scheduling Configuration ===
SCHEDULE_CONFIG = {
    "check_interval": 1,    # Check schedules every 1 second
    "max_schedule_age": 3600,  # Maximum age of schedules in seconds (1 hour)
}

# === Logging Configuration ===
LOGGING_CONFIG = {
    "level": logging.INFO,
    "format": '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    "log_file": "farmflow_server.log",  # Relative path - will be created in current directory
    "max_log_size": 10 * 1024 * 1024,  # 10MB
    "backup_count": 5,
}

# === Database Paths ===
DB_PATHS = {
    "gates": "farmflow/gates",
    "schedules": "farmflow/schedules", 
    "waterflow": "farmflow/waterflow_data",
    "sessions": "farmflow/waterflow_sessions",
    "server_status": "farmflow/server_status",
    "commands": "farmflow/gate_commands",
}

# === Message Patterns ===
MESSAGE_PATTERNS = {
    # Water flow patterns
    "waterflow_simple": r"F\|(\w+)\|([0-9.]+)\|(O|C)",
    "waterflow_detailed": r"FLOW\|(\w+)\|([0-9.]+)\|([^|]+)\|(open|closed|O|C)\|([^|]+)",
    
    # Location patterns
    "location": r"L\|(\w+)\|(.+)",
    
    # Status confirmation patterns
    "status_confirmation": r"STATUS\|(\w+)\|(OPEN|CLOSED)\|(CONFIRMED|FAILED)",
    "status_simple": r"(\w+)\|(OPEN|CLOSED)",  # Simple format: G001|OPEN
    "status_basic": r"(OPEN|CLOSED)",  # Basic format: just OPEN or CLOSED
}

# === Error Messages ===
ERROR_MESSAGES = {
    "sms_init_failed": "Failed to initialize SMS module",
    "firebase_init_failed": "Failed to initialize Firebase",
    "serial_connection_failed": "Failed to connect to serial port",
    "gate_timeout": "Gate operation timed out - no confirmation received",
    "invalid_message": "Invalid message format received",
    "unknown_gate": "Unknown gate ID received",
}

# === Success Messages ===
SUCCESS_MESSAGES = {
    "sms_sent": "SMS command sent successfully",
    "gate_confirmed": "Gate operation confirmed by magnet sensor",
    "schedule_executed": "Scheduled operation executed successfully",
    "data_processed": "Data processed and stored successfully",
}

# === Validation Functions ===
def validate_gate_id(gate_id):
    """Validate gate ID format and existence"""
    if not gate_id:
        return False
    
    # Allow any gate ID that starts with 'G' followed by digits
    # This is more flexible than hardcoded gate lists
    if gate_id.startswith('G') and gate_id[1:].isdigit():
        return True
    
    # Also check if gate exists in database (will be implemented by firebase_manager)
    return True  # For now, allow all valid format gate IDs

def validate_phone_number(phone_number):
    """Validate phone number format"""
    if not phone_number:
        return False
    # Basic validation for Philippine mobile numbers
    return phone_number.startswith('+639') and len(phone_number) == 13

def get_gate_phone(gate_id):
    """Get phone number for a gate ID"""
    return GATE_PHONES.get(gate_id)

def get_db_path(path_key):
    """Get database path by key"""
    return DB_PATHS.get(path_key)

# Add phone number normalization
def normalize_phone_number(phone: str) -> str:
    """Normalize phone number format"""
    if not phone:
        return phone
    
    # Remove spaces and special characters except +
    phone = ''.join(c for c in phone if c.isdigit() or c == '+')
    
    # Ensure it starts with +63 for Philippines
    if phone.startswith('63') and not phone.startswith('+63'):
        phone = '+' + phone
    elif phone.startswith('9') and len(phone) == 10:
        phone = '+63' + phone
    
    return phone

# === Environment Variables ===
def get_env_config():
    """Get configuration from environment variables"""
    env_config = {}
    
    # Override serial port if specified
    if os.getenv('FARMFLOW_SERIAL_PORT'):
        env_config['serial_port'] = os.getenv('FARMFLOW_SERIAL_PORT')
    
    # Override log level if specified
    if os.getenv('FARMFLOW_LOG_LEVEL'):
        level_map = {
            'DEBUG': logging.DEBUG,
            'INFO': logging.INFO,
            'WARNING': logging.WARNING,
            'ERROR': logging.ERROR,
            'CRITICAL': logging.CRITICAL
        }
        env_config['log_level'] = level_map.get(os.getenv('FARMFLOW_LOG_LEVEL'), logging.INFO)
    
    # Override Firebase database URL if specified
    if os.getenv('FARMFLOW_DATABASE_URL'):
        env_config['database_url'] = os.getenv('FARMFLOW_DATABASE_URL')
    
    return env_config

# === Configuration Validation ===
def validate_config():
    """Validate all configuration settings"""
    errors = []
    
    # Validate gate phone numbers
    for gate_id, phone in GATE_PHONES.items():
        if not validate_phone_number(phone):
            errors.append(f"Invalid phone number for gate {gate_id}: {phone}")
    
    # Validate Firebase config
    required_firebase_keys = ['apiKey', 'authDomain', 'databaseURL', 'projectId']
    for key in required_firebase_keys:
        if key not in FIREBASE_CONFIG or not FIREBASE_CONFIG[key]:
            errors.append(f"Missing or empty Firebase config key: {key}")
    
    # Validate timeouts
    if GATE_CONFIG['operation_timeout'] <= 0:
        errors.append("Gate operation timeout must be positive")
    
    if SMS_CONFIG['check_interval'] <= 0:
        errors.append("SMS check interval must be positive")
    
    return errors

# === Initialize Configuration ===
def init_config():
    """Initialize and validate configuration"""
    # Apply environment overrides
    env_config = get_env_config()
    
    # Update global config with environment variables
    if 'serial_port' in env_config:
        global SERIAL_PORT
        SERIAL_PORT = env_config['serial_port']
    
    if 'log_level' in env_config:
        LOGGING_CONFIG['level'] = env_config['log_level']
    
    if 'database_url' in env_config:
        FIREBASE_CONFIG['databaseURL'] = env_config['database_url']
    
    # Validate configuration
    errors = validate_config()
    if errors:
        raise ValueError(f"Configuration validation failed: {'; '.join(errors)}")
    
    return True

# === Export commonly used items ===
__all__ = [
    'FIREBASE_CONFIG', 'GATE_PHONES', 'SMS_CONFIG', 'GATE_CONFIG', 
    'SCHEDULE_CONFIG', 'LOGGING_CONFIG', 'DB_PATHS', 'MESSAGE_PATTERNS',
    'ERROR_MESSAGES', 'SUCCESS_MESSAGES', 'SERIAL_PORT', 'SERIAL_BAUD_RATE',
    'SERIAL_TIMEOUT', 'IS_LINUX', 'validate_gate_id', 'validate_phone_number',
    'get_gate_phone', 'get_db_path', 'init_config'
]












