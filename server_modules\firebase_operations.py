#!/usr/bin/env python3
"""
Firebase Operations module for FarmFlow Orange Pi Server
Handles all Firebase database operations and data processing
"""

import datetime
import logging
import pyrebase
try:
    from .config import FIREBASE_CONFIG, DB_PATHS, validate_gate_id
except ImportError:
    from config import FIREBASE_CONFIG, DB_PATHS, validate_gate_id

# Set up logging
logger = logging.getLogger("FirebaseOperations")


class FirebaseManager:
    """Manages all Firebase database operations"""
    
    def __init__(self):
        self.db = None
        self.firebase = None
        self.is_connected = False
        self._init_firebase()
    
    def _init_firebase(self):
        """Initialize Firebase connection"""
        try:
            self.firebase = pyrebase.initialize_app(FIREBASE_CONFIG)
            self.db = self.firebase.database()
            self.is_connected = True
            logger.info("Firebase initialized successfully")
            
            # Test connection
            self._test_connection()
            
        except Exception as e:
            logger.error(f"Failed to initialize Firebase: {e}")
            self.is_connected = False
            raise
    
    def _test_connection(self):
        """Test Firebase connection"""
        try:
            test_data = {
                "timestamp": datetime.datetime.now().isoformat(),
                "status": "connected"
            }
            self.db.child("farmflow").child("server_status").update(test_data)
            logger.info("Firebase connection test successful")
        except Exception as e:
            logger.error(f"Firebase connection test failed: {e}")
            self.is_connected = False
    
    def is_ready(self):
        """Check if Firebase is ready"""
        return self.is_connected
    
    # === Gate Operations ===
    
    def get_gate_data(self, gate_id):
        """Get gate data from Firebase"""
        try:
            logger.debug(f"[FIREBASE] Getting data for gate {gate_id}")
            
            gate_ref = self.db.child(f"farmflow/gates/{gate_id}")
            result = gate_ref.get()
            
            if result.val():
                return result.val()
            else:
                logger.warning(f"[FIREBASE] No data found for gate {gate_id}")
                return None
                
        except Exception as e:
            logger.error(f"[FIREBASE] Error getting gate data for {gate_id}: {e}")
            return None
    
    def update_gate_data(self, gate_id, update_data):
        """Update gate data in Firebase"""
        try:
            logger.info(f"[FIREBASE] Updating gate {gate_id} with data: {update_data}")
            
            # Ensure we have a valid gate_id
            if not gate_id:
                logger.error("[FIREBASE] No gate_id provided")
                return False
            
            # Update the gate data
            gate_ref = self.db.child(f"farmflow/gates/{gate_id}")
            gate_ref.update(update_data)
            
            logger.info(f"[FIREBASE] Successfully updated gate {gate_id}")
            return True
            
        except Exception as e:
            logger.error(f"[FIREBASE] Failed to update gate {gate_id}: {e}")
            return False
    
    def get_all_gates(self):
        """Get data for all gates"""
        try:
            gates_data = self.db.child(DB_PATHS["gates"]).get()
            if gates_data.each():
                return {gate.key(): gate.val() for gate in gates_data.each()}
            return {}
        except Exception as e:
            logger.error(f"Failed to get all gates data: {e}")
            return {}
    
    # === Waterflow Data Operations ===
    
    def store_waterflow_data(self, gate_id, waterflow_data, record_id):
        """Store waterflow data"""
        try:
            if not validate_gate_id(gate_id):
                logger.warning(f"Invalid gate ID: {gate_id}")
                return False
            
            # Store in gate-specific waterflow data
            self.db.child(DB_PATHS["gates"]).child(gate_id).child("waterflow_data").child(record_id).set(waterflow_data)
            
            # Also store in global waterflow data for reporting
            self.db.child(DB_PATHS["waterflow"]).child(gate_id).child(record_id).set(waterflow_data)
            
            logger.debug(f"Stored waterflow data for gate {gate_id}")
            return True
        except Exception as e:
            logger.error(f"Failed to store waterflow data for {gate_id}: {e}")
            return False
    
    def get_waterflow_sessions(self, gate_id):
        """Get waterflow sessions for a gate"""
        try:
            if not validate_gate_id(gate_id):
                return []
            
            sessions = self.db.child(DB_PATHS["sessions"]).child(gate_id).get()
            if sessions.each():
                return [session.val() for session in sessions.each()]
            return []
        except Exception as e:
            logger.error(f"Failed to get waterflow sessions for {gate_id}: {e}")
            return []
    
    def update_waterflow_session(self, gate_id, session_id, session_data):
        """Update waterflow session"""
        try:
            if not validate_gate_id(gate_id):
                return False
            
            self.db.child(DB_PATHS["sessions"]).child(gate_id).child(session_id).update(session_data)
            logger.debug(f"Updated waterflow session {session_id} for gate {gate_id}")
            return True
        except Exception as e:
            logger.error(f"Failed to update waterflow session for {gate_id}: {e}")
            return False
    
    # === Location Data Operations ===
    
    def store_location_data(self, gate_id, location_data, record_id):
        """Store location data"""
        try:
            if not validate_gate_id(gate_id):
                logger.warning(f"Invalid gate ID: {gate_id}")
                return False
            
            # Store location data
            self.db.child(DB_PATHS["gates"]).child(gate_id).child("location_data").child(record_id).set(location_data)
            
            # Update gate's main location fields
            gate_update = {
                "lastLocationUpdate": location_data.get("timestamp"),
                "locationRequestStatus": "completed"
            }
            
            # If we have valid coordinates, update the gate's latitude and longitude
            if location_data.get("locationType") == "coordinates":
                gate_update["latitude"] = location_data.get("latitude")
                gate_update["longitude"] = location_data.get("longitude")
                gate_update["hasLocation"] = True
                gate_update["locationStatus"] = "located"
                logger.info(f"Updated coordinates for gate {gate_id}")
            else:
                gate_update["hasLocation"] = False
                gate_update["locationStatus"] = "gps_signal_lost"
                logger.warning(f"No valid coordinates received for gate {gate_id}")
            
            self.db.child(DB_PATHS["gates"]).child(gate_id).update(gate_update)
            logger.debug(f"Stored location data for gate {gate_id}")
            return True
        except Exception as e:
            logger.error(f"Failed to store location data for {gate_id}: {e}")
            return False
    
    # === Status Confirmation Operations ===
    
    def store_status_confirmation(self, gate_id, status_data, record_id):
        """Store status confirmation data"""
        try:
            if not validate_gate_id(gate_id):
                logger.warning(f"Invalid gate ID: {gate_id}")
                return False
            
            # Store status confirmation data
            self.db.child(DB_PATHS["gates"]).child(gate_id).child("status_confirmations").child(record_id).set(status_data)
            
            # Get current gate data to check if this matches a pending operation
            current_gate_data = self.get_gate_data(gate_id)
            if not current_gate_data:
                logger.warning(f"No gate data found for gate {gate_id}")
                return False
            
            confirmed_status = status_data.get("status")
            confirmation_result = status_data.get("confirmation")
            timestamp = status_data.get("timestamp")
            
            current_status = current_gate_data.get("gateStatus", "unknown")
            operation_status = current_gate_data.get("operationStatus", "completed")
            
            # Check if this confirmation matches a pending operation
            expected_pending_status = f"pending_{confirmed_status}"
            
            if current_status == expected_pending_status and operation_status == "in_progress":
                # This confirmation matches the pending operation
                if confirmation_result == "confirmed":
                    # Operation successful - update to confirmed status
                    final_status = confirmed_status
                    gate_update = {
                        "gateStatus": final_status,
                        "operationStatus": "completed",
                        "lastUpdated": timestamp,
                        "lastConfirmation": timestamp,
                        "confirmationResult": "success",
                        "magnetSensorStatus": "working",
                        "pendingConfirmation": False
                    }
                    logger.info(f"Gate {gate_id} operation confirmed: {final_status}")
                else:
                    # Operation failed - revert to previous status or set to error
                    gate_update = {
                        "operationStatus": "failed",
                        "lastConfirmation": timestamp,
                        "confirmationResult": "failed",
                        "errorMessage": "Magnet sensor reported operation failed",
                        "magnetSensorStatus": "error",
                        "pendingConfirmation": False
                    }
                    logger.warning(f"Gate {gate_id} operation failed according to magnet sensor")
                
                self.update_gate_data(gate_id, gate_update)
            else:
                # This confirmation doesn't match any pending operation
                logger.warning(f"Received unexpected status confirmation for gate {gate_id}: {confirmed_status}")
                
                # Still update the magnet sensor status for monitoring
                self.update_gate_data(gate_id, {
                    "lastMagnetSensorReport": timestamp,
                    "magnetSensorStatus": "working" if confirmation_result == "confirmed" else "error"
                })
            
            logger.debug(f"Stored status confirmation for gate {gate_id}")
            return True
        except Exception as e:
            logger.error(f"Failed to store status confirmation for {gate_id}: {e}")
            return False
    
    # === Schedule Operations ===
    
    def get_active_schedules(self):
        """Get all active schedules"""
        try:
            schedules = self.db.child(DB_PATHS["schedules"]).get()
            if schedules.each():
                return {schedule.key(): schedule.val() for schedule in schedules.each()}
            return {}
        except Exception as e:
            logger.error(f"Failed to get schedules: {e}")
            return {}
    
    def update_schedule_status(self, schedule_id, status_data):
        """Update schedule execution status"""
        try:
            self.db.child(DB_PATHS["schedules"]).child(schedule_id).update(status_data)
            logger.debug(f"Updated schedule {schedule_id} status")
            return True
        except Exception as e:
            logger.error(f"Failed to update schedule {schedule_id}: {e}")
            return False
    
    # === Command Operations ===
    
    def get_pending_commands(self):
        """Get pending gate commands with detailed logging"""
        try:
            commands_path = DB_PATHS["commands"]  # farmflow/gate_commands
            logger.debug(f"[CMD] Checking commands at Firebase path: {commands_path}")

            commands = self.db.child(commands_path).get()

            if commands.each():
                command_dict = {cmd.key(): cmd.val() for cmd in commands.each()}
                logger.info(f"[CMD] Found {len(command_dict)} commands at path: {commands_path}")

                # Log command details for debugging
                for cmd_id, cmd_data in command_dict.items():
                    logger.debug(f"[CMD] Command {cmd_id}: {cmd_data}")

                return command_dict
            else:
                logger.debug(f"[CMD] No commands found at path: {commands_path}")
                return {}

        except Exception as e:
            logger.error(f"[CMD] Failed to get commands from {commands_path}: {e}")
            return {}
    
    def update_command_status(self, command_id, status_data):
        """Update command execution status"""
        try:
            self.db.child(DB_PATHS["commands"]).child(command_id).update(status_data)
            logger.debug(f"Updated command {command_id} status")
            return True
        except Exception as e:
            logger.error(f"Failed to update command {command_id}: {e}")
            return False
    
    def store_gate_command(self, command_id, command_data):
        """Store a gate command in the database with deduplication"""
        try:
            commands_path = DB_PATHS["commands"]  # farmflow/gate_commands
            logger.debug(f"[CMD] Storing command {command_id} at path: {commands_path}")

            # Check if command already exists
            existing_command = self.db.child(commands_path).child(command_id).get()
            if existing_command.val():
                logger.warning(f"[CMD] Command {command_id} already exists, skipping")
                return False

            # Add timestamp and processing flag
            command_data.update({
                "createdAt": datetime.datetime.now().isoformat(),
                "processed": False,
                "attempts": 0
            })

            self.db.child(commands_path).child(command_id).set(command_data)
            logger.info(f"[CMD] Command stored successfully: {command_id}")
            return True
        except Exception as e:
            logger.error(f"[CMD] Failed to store command {command_id}: {e}")
            return False

    def delete_command(self, command_id):
        """Delete a processed command from Firebase"""
        try:
            commands_path = DB_PATHS["commands"]  # farmflow/gate_commands
            command_ref = self.db.child(commands_path).child(command_id)
            
            logger.info(f"[CMD] 🗑️ Deleting command {command_id} from Firebase path: {commands_path}/{command_id}")
            
            # Remove the command
            command_ref.remove()
            
            # Verify the deletion
            verification = command_ref.get()
            if verification.val() is None:
                logger.info(f"[CMD] ✅ VERIFIED: Command {command_id} successfully deleted")
                return True
            else:
                logger.error(f"[CMD] ❌ FAILED: Command {command_id} still exists after deletion attempt")
                return False
            
        except Exception as e:
            logger.error(f"[CMD] Failed to delete command {command_id}: {e}")
            return False
    
    # === Server Status Operations ===
    
    def update_server_status(self, status_data):
        """Update server status"""
        try:
            status_data["lastUpdate"] = datetime.datetime.now().isoformat()
            self.db.child(DB_PATHS["server_status"]).update(status_data)
            return True
        except Exception as e:
            logger.error(f"Failed to update server status: {e}")
            return False

    def update_gate_status(self, gate_id: str, status: str, timestamp: str = None) -> bool:
        """Update gate status in Firebase with better error handling"""
        try:
            if timestamp is None:
                timestamp = datetime.now().isoformat()
            
            # Ensure gate_id is properly formatted
            if not gate_id.startswith('G'):
                gate_id = f"G{gate_id.zfill(3)}"
            
            # Update gate status
            gate_ref = self.db.reference(f'gates/{gate_id}')
            gate_ref.update({
                'status': status,
                'lastUpdated': timestamp,
                'lastStatusChange': timestamp
            })
            
            # Also update in gate_status collection
            status_ref = self.db.reference(f'gate_status/{gate_id}')
            status_ref.set({
                'status': status,
                'timestamp': timestamp,
                'gate_id': gate_id
            })
            
            return True
        
        except Exception as e:
            print(f"❌ Firebase update error: {e}")
            return False


# Global Firebase manager instance
firebase_manager = FirebaseManager()

# Convenience functions for backward compatibility
def get_database():
    """Get database instance (backward compatibility)"""
    return firebase_manager.db

def process_waterflow_data(gate_id, waterflow_data, record_id):
    """Process waterflow data (backward compatibility)"""
    return firebase_manager.store_waterflow_data(gate_id, waterflow_data, record_id)

def process_location_data(gate_id, location_data, record_id):
    """Process location data (backward compatibility)"""
    return firebase_manager.store_location_data(gate_id, location_data, record_id)

def process_status_confirmation(gate_id, status_data, record_id):
    """Process status confirmation (backward compatibility)"""
    return firebase_manager.store_status_confirmation(gate_id, status_data, record_id)











