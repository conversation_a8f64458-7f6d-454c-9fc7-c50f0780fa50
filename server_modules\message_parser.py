#!/usr/bin/env python3
"""
Message Parser module for FarmFlow Orange Pi Server
Handles parsing of SMS messages from gates (waterflow, location, status confirmation)
"""

import re
import datetime
import logging
try:
    from .config import MESSAGE_PATTERNS, validate_gate_id, ERROR_MESSAGES
except ImportError:
    from config import MESSAGE_PATTERNS, validate_gate_id, ERROR_MESSAGES

# Set up logging
logger = logging.getLogger("MessageParser")


class MessageParser:
    """Handles parsing of different types of SMS messages from gates"""
    
    def __init__(self):
        self.patterns = MESSAGE_PATTERNS
    
    def parse_message(self, message):
        """
        Parse an SMS message and determine its type and content
        
        Args:
            message (str): The SMS message content
            
        Returns:
            dict: Parsed message data with type and content, or None if invalid
        """
        if not message or not message.strip():
            logger.warning("Empty message received")
            return None
        
        message = message.strip()
        logger.debug(f"Parsing message: {message}")
        
        # Try parsing as waterflow data
        waterflow_data = self._parse_waterflow_message(message)
        if waterflow_data:
            return waterflow_data
        
        # Try parsing as location data
        location_data = self._parse_location_message(message)
        if location_data:
            return location_data
        
        # Try parsing as status confirmation
        status_data = self._parse_status_confirmation(message)
        if status_data:
            return status_data
        
        logger.warning(f"Unknown message format: {message}")
        return None
    
    def _parse_waterflow_message(self, message):
        """Parse waterflow SMS messages"""
        # Try simple format first: F|G001|flow_rate|O/C
        match = re.match(self.patterns["waterflow_simple"], message)
        if match:
            gate_id, flow_rate, status = match.groups()
            
            if not validate_gate_id(gate_id):
                logger.warning(f"Invalid gate ID in waterflow message: {gate_id}")
                return None
            
            try:
                flow_rate = float(flow_rate)
                status_full = "open" if status.upper() == "O" else "closed"
                
                return {
                    "dataType": "waterflow",
                    "format": "simple",
                    "gateId": gate_id,
                    "flowRate": flow_rate,
                    "status": status_full,
                    "timestamp": datetime.datetime.now().isoformat(),
                    "sessionId": None
                }
            except ValueError:
                logger.error(f"Invalid flow rate in message: {flow_rate}")
                return None
        
        # Try detailed format: FLOW|gate_id|flow_rate|timestamp|status|session_id
        match = re.match(self.patterns["waterflow_detailed"], message)
        if match:
            gate_id, flow_rate, timestamp, status, session_id = match.groups()
            
            if not validate_gate_id(gate_id):
                logger.warning(f"Invalid gate ID in waterflow message: {gate_id}")
                return None
            
            try:
                flow_rate = float(flow_rate)
                
                # Normalize status
                if status.upper() in ["O", "OPEN"]:
                    status_full = "open"
                elif status.upper() in ["C", "CLOSED"]:
                    status_full = "closed"
                else:
                    status_full = status.lower()
                
                return {
                    "dataType": "waterflow",
                    "format": "detailed",
                    "gateId": gate_id,
                    "flowRate": flow_rate,
                    "status": status_full,
                    "timestamp": timestamp,
                    "sessionId": session_id
                }
            except ValueError:
                logger.error(f"Invalid flow rate in detailed message: {flow_rate}")
                return None
        
        return None
    
    def _parse_location_message(self, message):
        """Parse location SMS messages"""
        match = re.match(self.patterns["location"], message)
        if not match:
            return None
        
        gate_id, location_data = match.groups()
        
        if not validate_gate_id(gate_id):
            logger.warning(f"Invalid gate ID in location message: {gate_id}")
            return None
        
        # Check if it's a valid GPS coordinate or NO_GPS_SIGNAL
        if location_data == "NO_GPS_SIGNAL":
            logger.info(f"Gate {gate_id} reported no GPS signal")
            return {
                "dataType": "location",
                "gateId": gate_id,
                "locationType": "no_signal",
                "locationData": "NO_GPS_SIGNAL",
                "timestamp": datetime.datetime.now().isoformat()
            }
        
        # Try to parse as latitude,longitude
        try:
            lat_lng = location_data.split(',')
            if len(lat_lng) == 2:
                latitude = float(lat_lng[0])
                longitude = float(lat_lng[1])
                
                # Basic validation for coordinates
                if -90 <= latitude <= 90 and -180 <= longitude <= 180:
                    return {
                        "dataType": "location",
                        "gateId": gate_id,
                        "locationType": "coordinates",
                        "latitude": latitude,
                        "longitude": longitude,
                        "timestamp": datetime.datetime.now().isoformat()
                    }
                else:
                    logger.warning(f"Invalid coordinate ranges: lat={latitude}, lng={longitude}")
                    return {
                        "dataType": "location",
                        "gateId": gate_id,
                        "locationType": "invalid",
                        "locationData": location_data,
                        "timestamp": datetime.datetime.now().isoformat(),
                        "error": "Coordinates out of valid range"
                    }
            else:
                logger.warning(f"Invalid location format: {location_data}")
                return {
                    "dataType": "location",
                    "gateId": gate_id,
                    "locationType": "invalid",
                    "locationData": location_data,
                    "timestamp": datetime.datetime.now().isoformat(),
                    "error": "Invalid coordinate format"
                }
        except ValueError:
            logger.warning(f"Invalid location coordinates: {location_data}")
            return {
                "dataType": "location",
                "gateId": gate_id,
                "locationType": "invalid",
                "locationData": location_data,
                "timestamp": datetime.datetime.now().isoformat(),
                "error": "Non-numeric coordinates"
            }
    
    def _parse_status_confirmation(self, message):
        """Parse status confirmation SMS messages from magnet sensor"""
        logger.debug(f"[PARSER] Trying to parse status confirmation: '{message}'")

        # Try full status confirmation format: STATUS|G001|OPEN|CONFIRMED
        match = re.match(self.patterns["status_confirmation"], message)
        if match:
            gate_id, status, confirmation = match.groups()

            if not validate_gate_id(gate_id):
                logger.warning(f"Invalid gate ID in status confirmation: {gate_id}")
                return None

            logger.info(f"[PARSER] ✅ Parsed full status confirmation: {gate_id} -> {status} ({confirmation})")
            return {
                "dataType": "status_confirmation",
                "gateId": gate_id,
                "status": status.lower(),
                "confirmation": confirmation.lower(),
                "timestamp": datetime.datetime.now().isoformat()
            }

        # Try simple status format: G001|OPEN
        match = re.match(self.patterns["status_simple"], message)
        if match:
            gate_id, status = match.groups()

            if not validate_gate_id(gate_id):
                logger.warning(f"Invalid gate ID in simple status: {gate_id}")
                return None

            logger.info(f"[PARSER] ✅ Parsed simple status: {gate_id} -> {status}")
            return {
                "dataType": "status_confirmation",
                "gateId": gate_id,
                "status": status.lower(),
                "confirmation": "confirmed",  # Assume confirmed for simple format
                "timestamp": datetime.datetime.now().isoformat()
            }

        # Try basic status format: just OPEN or CLOSED (need to infer gate from phone number)
        match = re.match(self.patterns["status_basic"], message)
        if match:
            status = match.groups()[0]

            logger.info(f"[PARSER] ✅ Parsed basic status: {status} (gate ID will be inferred from phone number)")
            return {
                "dataType": "status_confirmation",
                "gateId": "UNKNOWN",  # Will be filled by caller using phone number
                "status": status.lower(),
                "confirmation": "confirmed",
                "timestamp": datetime.datetime.now().isoformat()
            }

        logger.debug(f"[PARSER] ❌ No status confirmation pattern matched for: '{message}'")
        return None
    
    def validate_message_format(self, message):
        """
        Validate if a message matches any known format
        
        Args:
            message (str): The message to validate
            
        Returns:
            tuple: (is_valid, message_type)
        """
        if not message or not message.strip():
            return False, "empty"
        
        message = message.strip()
        
        # Check each pattern
        for pattern_name, pattern in self.patterns.items():
            if re.match(pattern, message):
                if pattern_name.startswith("waterflow"):
                    return True, "waterflow"
                elif pattern_name == "location":
                    return True, "location"
                elif pattern_name in ["status_confirmation", "status_simple", "status_basic"]:
                    return True, "status_confirmation"
        
        return False, "unknown"
    
    def extract_gate_id(self, message):
        """
        Extract gate ID from any supported message format
        
        Args:
            message (str): The message to extract gate ID from
            
        Returns:
            str: Gate ID or None if not found
        """
        if not message:
            return None
        
        # Try all patterns to extract gate ID
        for pattern in self.patterns.values():
            match = re.match(pattern, message.strip())
            if match:
                # Gate ID is always the first group in our patterns
                gate_id = match.groups()[0]
                if validate_gate_id(gate_id):
                    return gate_id
        
        return None
    
    def get_message_summary(self, parsed_data):
        """
        Get a human-readable summary of parsed message data
        
        Args:
            parsed_data (dict): Parsed message data
            
        Returns:
            str: Human-readable summary
        """
        if not parsed_data:
            return "Invalid message"
        
        data_type = parsed_data.get("dataType", "unknown")
        gate_id = parsed_data.get("gateId", "unknown")
        
        if data_type == "waterflow":
            flow_rate = parsed_data.get("flowRate", 0)
            status = parsed_data.get("status", "unknown")
            return f"Gate {gate_id}: Water flow {flow_rate} L/min, status {status}"
        
        elif data_type == "location":
            location_type = parsed_data.get("locationType", "unknown")
            if location_type == "coordinates":
                lat = parsed_data.get("latitude", 0)
                lng = parsed_data.get("longitude", 0)
                return f"Gate {gate_id}: Location {lat:.6f}, {lng:.6f}"
            elif location_type == "no_signal":
                return f"Gate {gate_id}: No GPS signal"
            else:
                return f"Gate {gate_id}: Invalid location data"
        
        elif data_type == "status_confirmation":
            status = parsed_data.get("status", "unknown")
            confirmation = parsed_data.get("confirmation", "unknown")
            return f"Gate {gate_id}: Status {status} {confirmation}"
        
        return f"Gate {gate_id}: {data_type} data"

    def parse_sms_message(self, message_text, sender_phone):
        """Parse incoming SMS message and extract gate status"""
        try:
            logger.info(f"[PARSER] Parsing message: '{message_text}' from {sender_phone}")
            
            # Normalize the message
            message = message_text.strip().upper()
            
            # Handle different message formats
            if '|' in message:
                # Format: G009|CLOSED or G009|OPENED
                parts = message.split('|')
                if len(parts) >= 2:
                    gate_id = parts[0].strip()
                    status = parts[1].strip()
                    
                    # Normalize status
                    if status in ['CLOSED', 'CLOSE']:
                        status = 'closed'
                    elif status in ['OPENED', 'OPEN']:
                        status = 'open'
                    
                    return {
                        'gate_id': gate_id,
                        'status': status,
                        'data_type': 'gate_status',
                        'confirmation_type': 'status_update',
                        'raw_message': message_text,
                        'timestamp': datetime.datetime.now().isoformat()
                    }
            
            # Handle simple status messages (when gate responds with just status)
            elif message in ['CLOSED', 'CLOSE']:
                gate_id = self._get_gate_by_phone(sender_phone)
                if gate_id:
                    return {
                        'gate_id': gate_id,
                        'status': 'closed',
                        'data_type': 'status_confirmation',
                        'confirmation_type': 'command_response',
                        'raw_message': message_text,
                        'timestamp': datetime.datetime.now().isoformat()
                    }
            
            elif message in ['OPENED', 'OPEN']:
                gate_id = self._get_gate_by_phone(sender_phone)
                if gate_id:
                    return {
                        'gate_id': gate_id,
                        'status': 'open',
                        'data_type': 'status_confirmation',
                        'confirmation_type': 'command_response',
                        'raw_message': message_text,
                        'timestamp': datetime.datetime.now().isoformat()
                    }
            
            logger.warning(f"[PARSER] Could not parse message: '{message_text}'")
            return None
            
        except Exception as e:
            logger.error(f"[PARSER] Error parsing message: {e}")
            return None

    def _get_gate_by_phone(self, phone_number):
        """Get gate ID by phone number"""
        try:
            # This should be implemented to look up gate by phone in database
            # For now, return None and let the calling code handle it
            return None
        except Exception as e:
            logger.error(f"[PARSER] Error getting gate by phone: {e}")
            return None

# Global parser instance
message_parser = MessageParser()

# Convenience functions for backward compatibility
def parse_waterflow_sms(message):
    """Parse waterflow SMS message (backward compatibility)"""
    return message_parser.parse_message(message)

def parse_location_sms(message):
    """Parse location SMS message (backward compatibility)"""
    parsed = message_parser.parse_message(message)
    return parsed if parsed and parsed.get("dataType") == "location" else None

def parse_status_confirmation_sms(message):
    """Parse status confirmation SMS message (backward compatibility)"""
    parsed = message_parser.parse_message(message)
    return parsed if parsed and parsed.get("dataType") == "status_confirmation" else None






