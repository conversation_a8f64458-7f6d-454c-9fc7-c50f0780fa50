#!/usr/bin/env python3
"""
Main server module for FarmFlow Orange Pi Server
Coordinates all modules and handles the main execution logic
"""

import sys
import os
import time
import threading
import logging
import signal
from datetime import datetime

# Add the server_modules directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import all modules
try:
    from .config import init_config, LOGGING_CONFIG, SMS_CONFIG, GATE_CONFIG, SCHEDULE_CONFIG
    from .sms_handler import SMSHandler
    from .message_parser import message_parser
    from .firebase_operations import firebase_manager
    from .gate_controller import GateController
    from .scheduler import create_scheduler
except ImportError:
    from config import init_config, LOGGING_CONFIG, SMS_CONFIG, GATE_CONFIG, SCHEDULE_CONFIG
    from sms_handler import SMSHandler
    from message_parser import message_parser
    from firebase_operations import firebase_manager
    from gate_controller import GateController
    from scheduler import create_scheduler

# Set up logging with error handling
def setup_logging():
    """Setup logging with proper error handling"""
    try:
        # Create logs directory if it doesn't exist
        log_file = LOGGING_CONFIG["log_file"]
        log_dir = os.path.dirname(log_file) if os.path.dirname(log_file) else "."

        if not os.path.exists(log_dir):
            os.makedirs(log_dir, exist_ok=True)

        # Setup logging
        logging.basicConfig(
            level=LOGGING_CONFIG["level"],
            format=LOGGING_CONFIG["format"],
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler(sys.stdout)
            ]
        )

    except Exception as e:
        # Fallback to console-only logging if file logging fails
        logging.basicConfig(
            level=LOGGING_CONFIG["level"],
            format=LOGGING_CONFIG["format"],
            handlers=[logging.StreamHandler(sys.stdout)]
        )
        print(f"Warning: Could not setup file logging ({e}), using console only")

# Initialize logging
setup_logging()
logger = logging.getLogger("FarmFlowServer")


class FarmFlowServer:
    """Main FarmFlow Orange Pi Server class"""
    
    def __init__(self):
        self.running = False
        self.sms_handler = None
        self.gate_controller = None
        self.scheduler = None
        self.threads = []
        self.processed_message_hashes = set()
        
        # Initialize all components
        self._initialize_components()
    
    def _initialize_components(self):
        """Initialize all server components"""
        try:
            logger.info("=== FarmFlow Orange Pi Server Starting ===")
            
            # Initialize configuration
            logger.info("Initializing configuration...")
            init_config()
            
            # Initialize SMS handler
            logger.info("Initializing SMS handler...")
            self.sms_handler = SMSHandler()
            if not self.sms_handler.is_ready():
                logger.warning("SMS handler not fully initialized, continuing with limited functionality")
            
            # Initialize Firebase
            logger.info("Initializing Firebase...")
            if not firebase_manager.is_ready():
                raise Exception("Firebase initialization failed")
            
            # Initialize gate controller
            logger.info("Initializing gate controller...")
            self.gate_controller = GateController(self.sms_handler)
            
            # Initialize scheduler
            logger.info("Initializing scheduler...")
            self.scheduler = create_scheduler(self.gate_controller)
            
            # Update server status
            firebase_manager.update_server_status({
                "status": "online",
                "version": "2.0",
                "components": {
                    "sms_handler": self.sms_handler.is_ready(),
                    "firebase": firebase_manager.is_ready(),
                    "gate_controller": True,
                    "scheduler": True
                }
            })
            
            logger.info("=== All components initialized successfully ===")
            
        except Exception as e:
            logger.error(f"Failed to initialize components: {e}")
            raise
    
    def start(self):
        """Start the server"""
        try:
            logger.info("Starting FarmFlow Orange Pi Server...")
            self.running = True
            
            # Start all background threads
            self._start_threads()
            
            # Set up signal handlers for graceful shutdown
            signal.signal(signal.SIGINT, self._signal_handler)
            signal.signal(signal.SIGTERM, self._signal_handler)
            
            logger.info("=== FarmFlow Orange Pi Server is running ===")
            logger.info("Press Ctrl+C to stop the server")
            
            # Main loop
            self._main_loop()
            
        except Exception as e:
            logger.error(f"Error starting server: {e}")
            self.stop()
            raise
    
    def _start_threads(self):
        """Start all background threads"""
        # SMS listener thread
        sms_thread = threading.Thread(target=self._sms_listener, daemon=True, name="SMS-Listener")
        sms_thread.start()
        self.threads.append(sms_thread)
        
        # Gate command listener thread
        command_thread = threading.Thread(target=self._command_listener, daemon=True, name="Command-Listener")
        command_thread.start()
        self.threads.append(command_thread)
        
        # Schedule checker thread
        schedule_thread = threading.Thread(target=self._schedule_checker, daemon=True, name="Schedule-Checker")
        schedule_thread.start()
        self.threads.append(schedule_thread)
        
        # Timeout checker thread
        timeout_thread = threading.Thread(target=self._timeout_checker, daemon=True, name="Timeout-Checker")
        timeout_thread.start()
        self.threads.append(timeout_thread)
        
        logger.info(f"Started {len(self.threads)} background threads")
    
    def _main_loop(self):
        """Main server loop"""
        try:
            while self.running:
                # Update server heartbeat
                firebase_manager.update_server_status({
                    "last_heartbeat": datetime.now().isoformat(),
                    "status": "running"
                })
                
                # Sleep for a minute before next heartbeat
                time.sleep(60)
                
        except KeyboardInterrupt:
            logger.info("Received keyboard interrupt")
        except Exception as e:
            logger.error(f"Error in main loop: {e}")
        finally:
            self.stop()
    
    def _sms_listener(self):
        """SMS listener thread"""
        logger.info("[SMS] Starting SMS listener thread")
        consecutive_errors = 0
        
        while self.running:
            try:
                # Read and process SMS messages
                self._read_and_process_sms()
                
                # Reset error counter on success
                if consecutive_errors > 0:
                    logger.info("[SMS] SMS listener recovered after errors")
                    consecutive_errors = 0
                
                # Wait before next check
                time.sleep(SMS_CONFIG["check_interval"])
                
            except Exception as e:
                consecutive_errors += 1
                logger.error(f"[SMS] Error in SMS listener (attempt {consecutive_errors}): {e}")
                
                if consecutive_errors >= GATE_CONFIG["max_consecutive_errors"]:
                    logger.critical(f"[SMS] Too many consecutive errors ({consecutive_errors}), stopping SMS listener")
                    break
                
                # Wait longer after errors
                time.sleep(SMS_CONFIG["check_interval"] * 2)
        
        logger.info("[SMS] SMS listener thread stopped")
    
    def _command_listener(self):
        """Gate command listener thread with proper command cleanup"""
        logger.info("[CMD] Starting command listener thread")
        
        while self.running:
            try:
                logger.debug("[CMD] Checking for new commands...")
                
                # Process Firebase commands
                self.gate_controller.process_firebase_commands()
                
                # Check for any leftover commands and clean them up
                self._cleanup_stale_commands()
                
                # Wait before next check
                time.sleep(5)  # Check every 5 seconds
                
            except Exception as e:
                logger.error(f"[CMD] Error in command listener: {e}")
                time.sleep(10)  # Wait longer on error
        
        logger.info("[CMD] Command listener thread stopped")

    def _cleanup_stale_commands(self):
        """Clean up commands that are older than 5 minutes"""
        try:
            commands_ref = firebase_manager.db.child("farmflow/gate_commands")
            commands_data = commands_ref.get()
            
            if not commands_data.val():
                return
            
            commands = commands_data.val()
            current_time = datetime.datetime.now()
            
            for command_id, command_data in commands.items():
                try:
                    created_at = command_data.get('createdAt')
                    if created_at:
                        cmd_time = datetime.datetime.fromisoformat(created_at)
                        age_minutes = (current_time - cmd_time).total_seconds() / 60
                        
                        if age_minutes > 5:  # Commands older than 5 minutes
                            logger.warning(f"[CLEANUP] Removing stale command {command_id} (age: {age_minutes:.1f} minutes)")
                            commands_ref.child(command_id).remove()
                            
                except Exception as cleanup_err:
                    logger.error(f"[CLEANUP] Error cleaning up command {command_id}: {cleanup_err}")
                    
        except Exception as e:
            logger.error(f"[CLEANUP] Error in cleanup process: {e}")
    
    def _schedule_checker(self):
        """Schedule checker thread"""
        logger.info("[SCHEDULE] Starting schedule checker thread")
        
        while self.running:
            try:
                # Check and execute schedules
                self.scheduler.check_schedules()
                
                # Cleanup old schedules every hour
                current_time = datetime.now()
                if current_time.minute == 0 and current_time.second < SCHEDULE_CONFIG["check_interval"]:
                    self.scheduler.cleanup_old_schedules()
                
                # Wait before next check
                time.sleep(SCHEDULE_CONFIG["check_interval"])
                
            except Exception as e:
                logger.error(f"[SCHEDULE] Error in schedule checker: {e}")
                time.sleep(SCHEDULE_CONFIG["check_interval"] * 2)
        
        logger.info("[SCHEDULE] Schedule checker thread stopped")
    
    def _timeout_checker(self):
        """Timeout checker thread"""
        logger.info("[TIMEOUT] Starting timeout checker thread")
        
        while self.running:
            try:
                # Check for timed out operations
                self.gate_controller.check_pending_operations_timeout()
                
                # Wait before next check
                time.sleep(GATE_CONFIG["status_check_interval"])
                
            except Exception as e:
                logger.error(f"[TIMEOUT] Error in timeout checker: {e}")
                time.sleep(GATE_CONFIG["status_check_interval"] * 2)
        
        logger.info("[TIMEOUT] Timeout checker thread stopped")
    
    def _read_and_process_sms(self):
        """Read and process SMS messages"""
        try:
            # Read SMS messages
            sms_list = self.sms_handler.read_all_sms()
            
            if not sms_list:
                logger.debug("[SMS] No SMS messages found")
                return
            
            logger.info(f"[SMS] Found {len(sms_list)} SMS messages")
            
            # Process each SMS message
            for sms in sms_list:
                try:
                    sender = sms.get('Number', 'unknown')
                    message = sms.get('Text', '')
                    location = sms.get('Location', 0)
                    
                    logger.info(f"[SMS] Processing message from {sender}: {message}")
                    
                    # Skip empty messages
                    if not message or message.isspace():
                        logger.warning(f"[SMS] Empty message from {sender}, skipping")
                        self.sms_handler.delete_sms(location)
                        continue
                    
                    # Check for duplicates
                    message_hash = f"{sender}:{message}"
                    if message_hash in self.processed_message_hashes:
                        logger.warning(f"[SMS] Duplicate message detected, skipping: {message}")
                        self.sms_handler.delete_sms(location)
                        continue
                    
                    # Parse the message
                    parsed_data = message_parser.parse_message(message)
                    
                    if parsed_data:
                        # Process the parsed data
                        self._process_parsed_message(parsed_data, location, sender)
                        
                        # Add to processed messages
                        self.processed_message_hashes.add(message_hash)
                        
                        # Keep only recent hashes (last 1000)
                        if len(self.processed_message_hashes) > 1000:
                            # Remove oldest 100 hashes
                            old_hashes = list(self.processed_message_hashes)[:100]
                            for old_hash in old_hashes:
                                self.processed_message_hashes.discard(old_hash)
                    else:
                        logger.warning(f"[SMS] Could not parse message from {sender}: {message}")
                    
                    # Delete processed SMS
                    self.sms_handler.delete_sms(location)
                    
                except Exception as sms_err:
                    logger.error(f"[SMS] Error processing SMS: {sms_err}")
        
        except Exception as e:
            logger.error(f"[SMS] Error reading SMS messages: {e}")
    
    def _process_parsed_message(self, parsed_data, sms_location, sender_phone):
        """Process parsed message data"""
        try:
            data_type = parsed_data.get("dataType")
            gate_id = parsed_data.get("gateId")

            # Handle case where gate ID is unknown (basic status format)
            if gate_id == "UNKNOWN":
                # Try to find gate ID from phone number
                gate_id = self._find_gate_by_phone(sender_phone)
                if gate_id:
                    parsed_data["gateId"] = gate_id
                    logger.info(f"[DATA] Inferred gate ID {gate_id} from phone number {sender_phone}")
                else:
                    logger.warning(f"[DATA] Could not find gate for phone number {sender_phone}")
                    return

            record_id = f"{gate_id}_{int(time.time())}_{sms_location}"

            logger.info(f"[DATA] Processing {data_type} data for gate {gate_id}")

            if data_type == "waterflow":
                firebase_manager.store_waterflow_data(gate_id, parsed_data, record_id)

                # Handle session management
                status = parsed_data.get("status")
                if status:
                    self.gate_controller.handle_gate_status_change(gate_id, status)

            elif data_type == "location":
                firebase_manager.store_location_data(gate_id, parsed_data, record_id)

            elif data_type == "status_confirmation":
                # Store status confirmation and update gate status
                firebase_manager.store_status_confirmation(gate_id, parsed_data, record_id)

                # Update gate status in database
                status = parsed_data.get("status")
                if status:
                    logger.info(f"[DATA] Updating gate {gate_id} status to {status}")
                    self.gate_controller.handle_gate_status_change(gate_id, status)

            else:
                logger.warning(f"[DATA] Unknown data type: {data_type}")

        except Exception as e:
            logger.error(f"[DATA] Error processing parsed message: {e}")

    def _find_gate_by_phone(self, phone_number):
        """Find gate ID by phone number from database only"""
        try:
            # Search in database only (remove config lookup)
            try:
                gates_data = firebase_manager.db.child("farmflow/gates").get()
                if gates_data.each():
                    for gate_item in gates_data.each():
                        gate_data = gate_item.val()
                        if gate_data and gate_data.get("phoneNumber") == phone_number:
                            return gate_item.key()
            except Exception as db_err:
                logger.warning(f"[DATA] Could not search database for phone {phone_number}: {db_err}")

            logger.warning(f"[DATA] No gate found for phone number {phone_number}")
            return None

        except Exception as e:
            logger.error(f"[DATA] Error finding gate by phone: {e}")
            return None
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        logger.info(f"Received signal {signum}, shutting down...")
        self.stop()
    
    def stop(self):
        """Stop the server"""
        if not self.running:
            return
        
        logger.info("Stopping FarmFlow Orange Pi Server...")
        self.running = False
        
        # Update server status
        try:
            firebase_manager.update_server_status({
                "status": "offline",
                "last_shutdown": datetime.now().isoformat()
            })
        except:
            pass
        
        # Close SMS handler
        if self.sms_handler:
            self.sms_handler.close()
        
        logger.info("=== FarmFlow Orange Pi Server stopped ===")


def main():
    """Main entry point"""
    try:
        # Create and start server
        server = FarmFlowServer()
        server.start()
        
    except KeyboardInterrupt:
        logger.info("Server interrupted by user")
    except Exception as e:
        logger.error(f"Server failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()







