import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'services/database_service.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:intl/intl.dart';
import 'dart:async';
import 'screens/dashboard_screen.dart';

class HomeScreen extends StatefulWidget {
  final String selectedGateId;
  final int initialTabIndex;

  const HomeScreen({
    Key? key,
    required this.selectedGateId,
    this.initialTabIndex = 0, // Default to first tab (Control)
  }) : super(key: key);

  @override
  HomeScreenState createState() => HomeScreenState();
}

class HomeScreenState extends State<HomeScreen> with SingleTickerProviderStateMixin {
  late DatabaseService _databaseService;
  bool _isGateOpen = false;
  bool _isLoadingInitialData = true;
  StreamSubscription? _gateStatusSubscription;
  StreamSubscription? _waterflowSubscription;
  double _currentWaterflow = 0.0;
  List<Map<String, dynamic>> _waterflowHistory = [];
  String? _currentSessionId;
  double _sessionTotal = 0.0;
  Timer? _scheduleCheckTimer;
  Timer? _waterflowUpdateTimer;

  // Tab controller
  late TabController _tabController;

  // Button state management
  bool _isCommandPending = false;
  StreamSubscription? _commandStatusSubscription;
  String? _pendingCommandId;

  // Get the selected gate ID from the widget
  String get selectedGateId => widget.selectedGateId;

  // Add tracking for pending commands per gate
  Map<String, bool> _gateCommandPending = {};
  Map<String, String?> _gatePendingCommandIds = {};
  Map<String, StreamSubscription?> _gateStatusSubscriptions = {};

  @override
  void initState() {
    super.initState();
    _databaseService = Provider.of<DatabaseService>(context, listen: false);

    // Initialize tab controller with 3 tabs: Control, Reports, Schedule
    _tabController = TabController(
      length: 3,
      vsync: this,
      initialIndex: widget.initialTabIndex, // Use the initialTabIndex from widget
    );

    // Load initial gate status from database first
    _loadInitialGateStatus();

    _setupGateStatusListener();
    _setupWaterflowListener();
    _startScheduleCheckTimer();
    _startWaterflowUpdateTimer();
  }

  /// Load initial gate status from database on app startup
  Future<void> _loadInitialGateStatus() async {
    try {
      print('🔄 Loading initial gate status from database for gate $selectedGateId');

      // Get gate data from database
      final gateData = await _databaseService.getGateData(selectedGateId);

      if (gateData != null) {
        final gateStatus = gateData['status'];
        final isOpen = (gateStatus == 'open');

        print('📊 Initial gate status from database: $gateStatus (isOpen: $isOpen)');

        // Update UI state with database status
        if (mounted) {
          setState(() {
            _isGateOpen = isOpen;
          });
        }

        // If gate is open, check for active session
        if (isOpen) {
          final sessionId = gateData['currentSessionId'];
          if (sessionId != null) {
            setState(() {
              _currentSessionId = sessionId;
            });
            print('📊 Found active session: $sessionId');
          }
        }

        // Check for pending commands
        final operationStatus = gateData['operationStatus'];
        final isPending = (operationStatus == 'pending' || operationStatus == 'in_progress');

        if (isPending) {
          setState(() {
            _isCommandPending = true;
            _gateCommandPending[selectedGateId] = true;
          });
          print('📊 Found pending command for gate $selectedGateId');
        }

        // Load current waterflow if gate is open
        if (isOpen) {
          final currentFlow = gateData['currentWaterflow'];
          if (currentFlow != null) {
            setState(() {
              _currentWaterflow = (currentFlow as num).toDouble();
            });
            print('📊 Initial waterflow: $_currentWaterflow L/min');
          }
        }
      } else {
        print('⚠️ No gate data found in database for gate $selectedGateId');
      }
    } catch (e) {
      print('❌ Error loading initial gate status: $e');
      // Keep default status (closed) if database read fails
    } finally {
      // Mark initial loading as complete
      if (mounted) {
        setState(() {
          _isLoadingInitialData = false;
        });
      }
    }
  }

  void _startScheduleCheckTimer() {
    // Check schedules every second
    _scheduleCheckTimer = Timer.periodic(Duration(seconds: 1), (timer) {
      _checkSchedules();
    });
  }

  void _startWaterflowUpdateTimer() {
    // Cancel any existing timer
    _waterflowUpdateTimer?.cancel();

    // Update session total every 10 seconds if the gate is open
    // This doesn't generate mock data, just updates the UI with real data from the database
    _waterflowUpdateTimer = Timer.periodic(Duration(seconds: 10), (timer) {
      if (_isGateOpen && _currentSessionId != null) {
        // Only update the session total from the database
        if (mounted) {
          _databaseService.getGateSessionTotalWaterUsage(
            selectedGateId,
            _currentSessionId!
          ).then((total) {
            setState(() {
              _sessionTotal = total;
            });
          });
        }
      }
    });
  }

  Future<void> _checkSchedules() async {
    try {
      // Get schedules for this specific gate
      final snapshot = await _databaseService.getGateSchedules(selectedGateId);
      if (snapshot.isEmpty) return;

      final now = DateTime.now();

      for (var schedule in snapshot) {
        try {
          // Skip completed schedules
          if (schedule['isCompleted'] == true) continue;

          // Parse schedule time
          final scheduleTime = _parseDateTime(schedule['dateTime']?.toString() ?? '');

          // Check if it's time to execute the schedule
          // We consider it's time if the current time is within 1 second of the scheduled time
          final difference = now.difference(scheduleTime);
          if (difference.inSeconds.abs() <= 1) {
            print('Schedule ${schedule['id']} is due for execution now!');

            // Execute the schedule
            await _executeSchedule(schedule);
          }
        } catch (e) {
          print('Error processing schedule: $e');
        }
      }
    } catch (e) {
      print('Error checking schedules: $e');
    }
  }

  Future<void> _executeSchedule(Map<String, dynamic> schedule) async {
    try {
      final scheduleId = schedule['id'];
      final action = schedule['action'] ?? 'open';
      final timestamp = DateTime.now().toIso8601String();

      print('🕐 Executing schedule $scheduleId with action: $action');

      // Mark the schedule as executing
      await _databaseService.updateGateSchedule(
        selectedGateId,
        scheduleId,
        {
          'isExecuting': true,
          'executionStartedAt': timestamp,
        }
      );

      // Send command to Orange Pi server via Firebase (using command system)
      await _sendScheduledGateCommand(selectedGateId, action.toUpperCase(), scheduleId);

      // Record the operation
      await _databaseService.recordGateOperation(
        selectedGateId,
        'scheduled_execution',
        {
          'scheduleId': scheduleId,
          'action': action,
          'status': 'command_sent',
          'timestamp': timestamp,
          'description': 'Scheduled $action command sent for Gate $selectedGateId',
        }
      );

      // Mark the schedule as completed
      await _databaseService.updateGateSchedule(
        selectedGateId,
        scheduleId,
        {
          'isCompleted': true,
          'isExecuting': false,
          'executedAt': timestamp,
          'executionStatus': 'command_sent',
        }
      );

      // Handle recurring schedules
      if (schedule['isRecurring'] == true) {
        await _createNextRecurringSchedule(schedule);
      }

      print('✅ Schedule $scheduleId command sent successfully');
    } catch (e) {
      print('❌ Error executing schedule: $e');
    }
  }

  /// Send scheduled command via database service
  Future<void> _sendScheduledGateCommand(String gateId, String action, String scheduleId) async {
    print('🚀 Starting _sendScheduledGateCommand: $action for gate $gateId (schedule: $scheduleId)');

    try {
      // Use database service to send command - Orange Pi will fetch and execute
      if (action.toUpperCase() == 'OPEN') {
        await _databaseService.sendOpenCommand(gateId);
        print('✅ Scheduled OPEN command sent via database service');
      } else if (action.toUpperCase() == 'CLOSE') {
        await _databaseService.sendCloseCommand(gateId);
        print('✅ Scheduled CLOSE command sent via database service');
      }

      print('📤 Action: $action Gate: $gateId Schedule: $scheduleId');
      print('🔄 Orange Pi will fetch command from database and execute it');

    } catch (e, stackTrace) {
      print('❌ Failed to send scheduled command via database service: $e');
      print('❌ Stack trace: $stackTrace');
      rethrow;
    }
  }

  Future<void> _createNextRecurringSchedule(Map<String, dynamic> schedule) async {
    try {
      final scheduleId = schedule['id'];
      final recurringType = schedule['recurringType'] ?? 'daily';
      final scheduleTime = _parseDateTime(schedule['dateTime']?.toString() ?? '');

      // Calculate next occurrence based on recurring type
      DateTime nextDateTime;
      if (recurringType == 'daily') {
        nextDateTime = scheduleTime.add(Duration(days: 1));
      } else if (recurringType == 'weekly') {
        nextDateTime = scheduleTime.add(Duration(days: 7));
      } else if (recurringType == 'monthly') {
        // Add approximately one month (30 days)
        nextDateTime = scheduleTime.add(Duration(days: 30));
      } else {
        // Default to daily
        nextDateTime = scheduleTime.add(Duration(days: 1));
      }

      // Create a new schedule for the next occurrence
      final newSchedule = Map<String, dynamic>.from(schedule);
      newSchedule.remove('isCompleted');
      newSchedule.remove('executedAt');
      newSchedule.remove('isExecuting');
      newSchedule.remove('executionStartedAt');
      newSchedule.remove('executionStatus');
      newSchedule['dateTime'] = nextDateTime.toIso8601String();
      newSchedule['createdFromRecurring'] = true;
      newSchedule['previousScheduleId'] = scheduleId;
      newSchedule['createdAt'] = DateTime.now().toIso8601String();

      await _databaseService.saveGateSchedule(selectedGateId, newSchedule);
      print('Created new recurring schedule for $recurringType at ${nextDateTime.toIso8601String()}');
    } catch (e) {
      print('Error creating next recurring schedule: $e');
    }
  }

  void _setupGateStatusListener() {
    // Use the selectedGateId to listen to the specific gate's data
    // Use asBroadcastStream to allow multiple listeners
    final stream = _databaseService.listenToGateData(selectedGateId).asBroadcastStream();

    _gateStatusSubscription = stream.listen((event) {
      if (event.snapshot.exists) {
        final data = Map<String, dynamic>.from(event.snapshot.value as Map);
        final newGateStatus = data['gateStatus'] == 'open';
        final lastOperation = data['lastOperation'] ?? 'unknown';
        final scheduledExecutionId = data['scheduledExecutionId'];
        final realTimeUpdate = data['realTimeUpdate'] ?? false;

        print('Gate status update detected: ${data['gateStatus']} (Last operation: $lastOperation, Real-time: $realTimeUpdate)');

        // Always update the UI state to match the database
        if (mounted) {
          setState(() {
            // Always sync UI with database state
            _isGateOpen = newGateStatus;
            
            if (newGateStatus != _isGateOpen) {
              print('Gate status changed from ${_isGateOpen ? 'open' : 'closed'} to ${newGateStatus ? 'open' : 'closed'}');

              // Handle automatic session creation for scheduled openings
              if (newGateStatus) {
                _handleGateOpened(lastOperation, scheduledExecutionId);
              } else {
                _handleGateClosed(lastOperation, scheduledExecutionId);
              }

              // Log scheduled operations
              if (lastOperation == 'scheduled') {
                print('Gate $selectedGateId was automatically ${newGateStatus ? 'opened' : 'closed'} by schedule');
              }
            } else {
              // Even if the status didn't change, update other UI elements
              // This ensures the UI stays in sync with the database
              print('Gate status remains ${newGateStatus ? 'open' : 'closed'} (Last operation: $lastOperation)');
            }
          });
        }
      }
    });
  }

  // Handle gate opened event
  void _handleGateOpened(String operationType, String? scheduleId) {
    // Create a new session ID if one doesn't exist
    if (_currentSessionId == null) {
      _currentSessionId = DateTime.now().millisecondsSinceEpoch.toString();
      _sessionTotal = 0.0;

      // Set initial UI state without generating mock data
      setState(() {
        _currentWaterflow = 0.0; // No initial value - wait for real data
        _waterflowHistory = [];
      });

      // Initialize session tracking without generating mock water flow data
      _databaseService.registerActiveSession(selectedGateId, _currentSessionId!);
      print('New waterflow session created: $_currentSessionId');

      // Start the timer to update session total
      _startWaterflowUpdateTimer();
    }
  }

  // Handle gate closed event
  void _handleGateClosed(String operationType, String? scheduleId) {
    // Finalize the session if one exists
    if (_currentSessionId != null) {
      // Create a report for the completed session
      final sessionData = {
        'sessionId': _currentSessionId,
        'gateId': selectedGateId,
        'gateName': 'Gate $selectedGateId',
        'startTime': DateTime.now().subtract(Duration(minutes: 30)).toIso8601String(), // Approximate
        'endTime': DateTime.now().toIso8601String(),
        'totalWater': _sessionTotal,
        'status': 'completed',
        'description': 'Irrigation session completed ${operationType == 'scheduled' ? 'automatically by schedule' : 'manually'} for Gate $selectedGateId',
        'action': 'close',
        'closedBy': operationType,
        'scheduleId': scheduleId,
      };

      _databaseService.saveGateWaterflowSessionReport(selectedGateId, sessionData).then((_) {
        // Unregister this session from the database service
        _databaseService.unregisterActiveSession(selectedGateId);
        print('Waterflow session finalized: $_currentSessionId');
        _currentSessionId = null;
      }).catchError((e) {
        print('Error finalizing waterflow session: $e');
      });
    }
  }

  void _setupWaterflowListener() {
    // Use live gate-specific waterflow data for graphs
    // Use asBroadcastStream to allow multiple listeners
    final stream = _databaseService.listenToLiveGateWaterflowData(selectedGateId).asBroadcastStream();

    _waterflowSubscription = stream.listen((event) async {
      if (event.snapshot.exists) {
        final data = Map<String, dynamic>.from(event.snapshot.value as Map);

        if (data.isNotEmpty) {
          List<Map<String, dynamic>> entries = [];

          // Safely convert and cast the data
          for (var entry in data.values) {
            if (entry is Map) {
              entries.add(Map<String, dynamic>.from(entry));
            }
          }

          // Sort with error handling
          entries.sort((a, b) {
            try {
              String aTimestamp = a['timestamp']?.toString() ?? '';
              String bTimestamp = b['timestamp']?.toString() ?? '';

              // Handle timestamp|status format
              if (aTimestamp.contains('|')) {
                aTimestamp = aTimestamp.split('|')[0];
              }
              if (bTimestamp.contains('|')) {
                bTimestamp = bTimestamp.split('|')[0];
              }

              DateTime aTime, bTime;

              // Try parsing with different formats
              try {
                aTime = DateTime.parse(aTimestamp);
              } catch (e) {
                try {
                  aTime = DateTime.fromMillisecondsSinceEpoch(int.parse(aTimestamp));
                } catch (e2) {
                  aTime = DateTime.now(); // Fallback
                }
              }

              try {
                bTime = DateTime.parse(bTimestamp);
              } catch (e) {
                try {
                  bTime = DateTime.fromMillisecondsSinceEpoch(int.parse(bTimestamp));
                } catch (e2) {
                  bTime = DateTime.now(); // Fallback
                }
              }

              return bTime.compareTo(aTime); // Descending order
            } catch (e) {
              print('Error sorting waterflow entries: $e');
              return 0; // Keep original order if parsing fails
            }
          });

          if (entries.isNotEmpty) {
            final latestEntry = entries.first;

            if (_currentSessionId != null) {
              final sessionTotal = await _databaseService.getGateSessionTotalWaterUsage(
                selectedGateId,
                _currentSessionId!
              );

              if (mounted) {
                setState(() {
                  _currentWaterflow = latestEntry['flowRate']?.toDouble() ?? 0.0;
                  _waterflowHistory = entries;
                  _sessionTotal = sessionTotal;
                });
              }

              // We no longer generate mock water flow data here
              // The real data will come from the gate controller via SMS to the Orange Pi
            }
          }
        }
      }
    });
  }

  Future<void> _toggleGate() async {
    // Check if this specific gate already has a pending command
    if (_gateCommandPending[selectedGateId] == true) {
      print('⚠️ Gate $selectedGateId already has a pending command, ignoring click');
      return;
    }

    // Show loading state immediately when button is clicked
    setState(() {
      _isCommandPending = true;
      _gateCommandPending[selectedGateId] = true;
    });

    try {
      final currentStatus = _isGateOpen;
      final action = currentStatus ? 'close' : 'open';

      print('🎯 Toggle gate clicked: $selectedGateId to $action (current: ${currentStatus ? 'open' : 'closed'})');

      // Check if we're trying to send the same command that's already pending
      final existingCommandId = _gatePendingCommandIds[selectedGateId];
      if (existingCommandId != null) {
        print('⚠️ Gate $selectedGateId already has pending command: $existingCommandId');
        // Reset loading state if command already exists
        setState(() {
          _isCommandPending = false;
          _gateCommandPending[selectedGateId] = false;
        });
        return;
      }

      // Generate command ID for tracking
      final commandId = 'cmd_${selectedGateId}_${action.toLowerCase()}_${DateTime.now().millisecondsSinceEpoch}';

      // Check database for existing pending commands for this gate
      final hasExistingCommand = await _checkForExistingPendingCommand(selectedGateId, action);
      if (hasExistingCommand) {
        print('⚠️ Database already has pending $action command for gate $selectedGateId');
        // Reset loading state if command already exists
        setState(() {
          _isCommandPending = false;
          _gateCommandPending[selectedGateId] = false;
        });
        return;
      }

      // Update command tracking
      setState(() {
        _gatePendingCommandIds[selectedGateId] = commandId;
        _pendingCommandId = commandId;
      });

      // Start listening for database updates for this specific gate
      _startListeningForDatabaseUpdates(selectedGateId);

      // Send command to server via Firebase
      await _sendGateCommand(selectedGateId, action.toUpperCase());

      print('✅ Gate command sent, waiting for server confirmation...');

    } catch (e) {
      print('❌ Error sending gate command: $e');
      // Reset loading state on error
      setState(() {
        _gateCommandPending[selectedGateId] = false;
        _gatePendingCommandIds[selectedGateId] = null;
        _isCommandPending = false;
        _pendingCommandId = null;
      });
    }
  }

  Future<bool> _checkForExistingPendingCommand(String gateId, String action) async {
    try {
      // Let the database service handle checking for existing commands
      // Orange Pi will handle duplicate command detection
      print('🔄 Skipping duplicate command check - Orange Pi will handle this');
      return false; // Always allow command, let Orange Pi handle duplicates
    } catch (e) {
      print('❌ Error checking for existing commands: $e');
      return false; // Allow command if check fails
    }
  }

  Future<void> _sendGateCommand(String gateId, String action) async {
    try {
      print('📤 Sending $action command for gate $gateId via database service');

      // Use database service to send command - Orange Pi will fetch and execute
      if (action.toUpperCase() == 'OPEN') {
        await _databaseService.sendOpenCommand(gateId);
        print('✅ OPEN command sent via database service');
      } else if (action.toUpperCase() == 'CLOSE') {
        await _databaseService.sendCloseCommand(gateId);
        print('✅ CLOSE command sent via database service');
      }

      print('🔄 Orange Pi will fetch command from database and execute it');
      print('📤 Action: $action Gate: $gateId');

    } catch (e, stackTrace) {
      print('❌ Failed to send command via database service: $e');
      print('❌ Stack trace: $stackTrace');
      rethrow;
    }
  }

  Future<void> _requestGateLocation() async {
    try {
      print('🌍 Location request clicked for gate $selectedGateId');

      // Request location from the gate using command system
      await _databaseService.requestGateLocation(selectedGateId);

      print('📍 Location command sent to Gate $selectedGateId');
    } catch (e) {
      print('❌ Error requesting location for gate $selectedGateId: $e');
    }
  }

  @override
  void dispose() {
    // Cancel all gate status subscriptions
    for (var subscription in _gateStatusSubscriptions.values) {
      subscription?.cancel();
    }
    _gateStatusSubscriptions.clear();
    
    _gateStatusSubscription?.cancel();
    _waterflowSubscription?.cancel();
    _scheduleCheckTimer?.cancel();
    _waterflowUpdateTimer?.cancel();
    _commandStatusSubscription?.cancel();
    _tabController.dispose();
    super.dispose();
  }

  void _startListeningForDatabaseUpdates(String gateId) {
    // Cancel existing subscription for this gate
    _gateStatusSubscriptions[gateId]?.cancel();
    
    // Listen to database updates for this specific gate
    _gateStatusSubscriptions[gateId] = _databaseService.listenToGateData(gateId).listen((event) {
      if (event.snapshot.exists) {
        final data = Map<String, dynamic>.from(event.snapshot.value as Map);
        final gateStatus = data['gateStatus'] ?? '';
        final operationStatus = data['operationStatus'] ?? '';
        final serverMessage = data['serverMessage'] ?? '';
        final confirmationReceived = data['confirmationReceived'] ?? false;

        print('📊 Database update for gate $gateId: status=$gateStatus, operation=$operationStatus, confirmed=$confirmationReceived');

        // Update UI only if this is the currently selected gate
        if (gateId == selectedGateId && mounted) {
          setState(() {
            _isGateOpen = (gateStatus == 'open');
          });
        }

        // Handle command completion for this specific gate
        if (_gateCommandPending[gateId] == true) {
          if (operationStatus == 'completed' && confirmationReceived) {
            // Server confirmed the operation was successful
            setState(() {
              _gateCommandPending[gateId] = false;
              _gatePendingCommandIds[gateId] = null;
              if (gateId == selectedGateId) {
                _isCommandPending = false;
                _pendingCommandId = null;
              }
            });

            // Show success popup
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('✅ Gate $gateId ${gateStatus == 'open' ? 'opened' : 'closed'} successfully!'),
                  backgroundColor: Colors.green,
                  duration: Duration(seconds: 3),
                ),
              );
            }
            print('✅ Gate $gateId ${gateStatus == 'open' ? 'opened' : 'closed'} successfully!');
          }
          else if (operationStatus == 'failed') {
            // Server reported operation failed
            setState(() {
              _gateCommandPending[gateId] = false;
              _gatePendingCommandIds[gateId] = null;
              if (gateId == selectedGateId) {
                _isCommandPending = false;
                _pendingCommandId = null;
              }
            });

            // Show error popup
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('❌ Gate $gateId: ${serverMessage.isNotEmpty ? serverMessage : "Operation failed"}'),
                  backgroundColor: Colors.red,
                  duration: Duration(seconds: 4),
                ),
              );
            }
            print('❌ Gate $gateId: ${serverMessage.isNotEmpty ? serverMessage : "Operation failed"}');
          }
          else if (operationStatus == 'timeout') {
            // Server reported timeout
            setState(() {
              _gateCommandPending[gateId] = false;
              _gatePendingCommandIds[gateId] = null;
              if (gateId == selectedGateId) {
                _isCommandPending = false;
                _pendingCommandId = null;
              }
            });

            // Show timeout popup
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('⏰ Gate $gateId operation timed out'),
                  backgroundColor: Colors.orange,
                  duration: Duration(seconds: 4),
                ),
              );
            }
            print('⏰ Gate $gateId operation timed out');
          }
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Gate $selectedGateId'),
        backgroundColor: Theme.of(context).primaryColor,
        leading: IconButton(
          icon: Icon(Icons.arrow_back),
          onPressed: () {
            Navigator.pushReplacement(
              context,
              MaterialPageRoute(builder: (context) => DashboardScreen()),
            );
          },
        ),
        bottom: TabBar(
          controller: _tabController,
          tabs: [
            Tab(icon: Icon(Icons.door_front_door), text: 'Control'),
            Tab(icon: Icon(Icons.analytics), text: 'Reports'),
            Tab(icon: Icon(Icons.schedule), text: 'Schedule'),
          ],
          indicatorColor: Colors.white,
          labelColor: Colors.white,
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          // Tab 1: Control
          _isLoadingInitialData
            ? Center(
                child: Padding(
                  padding: const EdgeInsets.all(32.0),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CircularProgressIndicator(),
                      SizedBox(height: 16),
                      Text(
                        'Loading gate status from database...',
                        style: TextStyle(fontSize: 16, color: Colors.grey[600]),
                      ),
                    ],
                  ),
                ),
              )
            : SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Card(
                elevation: 4,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      Text(
                        'Gate Status',
                        style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                      ),
                      SizedBox(height: 16),
                      Icon(
                        _isGateOpen ? Icons.lock_open : Icons.lock,
                        size: 64,
                        color: _isGateOpen ? Colors.green : Colors.red,
                      ),
                      SizedBox(height: 16),
                      Text(
                        _isGateOpen ? 'Gate is OPEN' : 'Gate is CLOSE',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: _isGateOpen ? Colors.green : Colors.red,
                        ),
                      ),
                      SizedBox(height: 16),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          Expanded(
                            child: ElevatedButton(
                              onPressed: _isCommandPending ? null : _toggleGate,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: _isCommandPending
                                  ? Colors.grey  // Grey when disabled
                                  : (_isGateOpen ? Colors.red : Colors.green),
                                disabledBackgroundColor: Colors.grey,
                                padding: EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                              child: _isCommandPending
                                ? Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      SizedBox(
                                        width: 16,
                                        height: 16,
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2,
                                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                        ),
                                      ),
                                      SizedBox(width: 8),
                                      Text(
                                        'PROCESSING...',
                                        style: TextStyle(fontSize: 12, color: Colors.white),
                                      ),
                                    ],
                                  )
                                : Text(
                                    _isGateOpen ? 'CLOSE GATE' : 'OPEN GATE',
                                    style: TextStyle(fontSize: 16, color: Colors.white),
                                  ),
                            ),
                          ),
                          SizedBox(width: 8),
                          Expanded(
                            child: ElevatedButton.icon(
                              onPressed: _requestGateLocation,
                              icon: Icon(Icons.location_on, color: Colors.white),
                              label: Text(
                                'LOCATION',
                                style: TextStyle(fontSize: 16, color: Colors.white),
                              ),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.blue,
                                padding: EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              // Always show waterflow data card, but content changes based on gate status
              SizedBox(height: 16),
              Card(
                elevation: 4,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      Text(
                        'Waterflow Sensor Data',
                        style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                      ),
                      SizedBox(height: 16),
                      if (_isGateOpen) ...[
                        // Show current flow data when gate is open
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'Current Flow Rate:',
                              style: TextStyle(fontSize: 16),
                            ),
                            Text(
                              '${_currentWaterflow.toStringAsFixed(2)} L/s',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.blue,
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 8),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'Session Total:',
                              style: TextStyle(fontSize: 16),
                            ),
                            Text(
                              '${_sessionTotal.toStringAsFixed(2)} L',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.green,
                              ),
                            ),
                          ],
                        ),
                      ] else ...[
                        // Show gate closed message when gate is closed
                        Center(
                          child: Padding(
                            padding: const EdgeInsets.symmetric(vertical: 8.0),
                            child: Column(
                              children: [
                                Icon(Icons.water_drop_outlined, color: Colors.red.withAlpha(128), size: 48),
                                SizedBox(height: 8),
                                Text(
                                  'Gate is currently closed',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.red,
                                  ),
                                ),
                                SizedBox(height: 4),
                                Text(
                                  'No active water flow',
                                  style: TextStyle(color: Colors.grey),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                      SizedBox(height: 16),
                      Text(
                        'Recent Flow History:',
                        style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                      ),
                      SizedBox(height: 8),
                      SizedBox(
                        height: 200,
                        child: _waterflowHistory.isEmpty
                            ? Center(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(Icons.water_drop, color: Colors.blue.withAlpha(128), size: 48),
                                    SizedBox(height: 16),
                                    Text(
                                      'No water flow data available',
                                      style: TextStyle(color: Colors.grey),
                                    ),
                                    SizedBox(height: 8),
                                    Text(
                                      _isGateOpen
                                          ? 'Waiting for sensor readings...'
                                          : 'Open the gate to record water flow',
                                      style: TextStyle(color: Colors.blue),
                                    ),
                                  ],
                                ),
                              )
                            : ListView.builder(
                                itemCount: _waterflowHistory.length > 5 ? 5 : _waterflowHistory.length,
                                itemBuilder: (context, index) {
                                  final entry = _waterflowHistory[index];
                                  final timestamp = _parseDateTime(entry['timestamp']?.toString() ?? '');
                                  final flowRate = (entry['flowRate'] as num?)?.toDouble() ?? 0.0;
                                  return ListTile(
                                    title: Text('${(flowRate).toStringAsFixed(2)} L/s'),
                                    subtitle: Text(DateFormat('yyyy-MM-dd HH:mm:ss').format(timestamp)),
                                    trailing: Icon(Icons.water_drop, color: Colors.blue),
                                  );
                                },
                              ),
                      ),
                    ],
                  ),
                ),
              ),
              SizedBox(height: 16),
              StreamBuilder<DatabaseEvent>(
                stream: _databaseService.listenToGateWaterflowReports(selectedGateId).asBroadcastStream(),
                builder: (context, snapshot) {
                  if (snapshot.hasData && snapshot.data!.snapshot.exists) {
                    final reports = Map<String, dynamic>.from(snapshot.data!.snapshot.value as Map);

                    // Sort reports by timestamp (most recent first)
                    List<dynamic> sortedReports = reports.values.toList();

                    // Sort with error handling
                    sortedReports.sort((a, b) {
                      try {
                        String aTimestamp = a['timestamp']?.toString() ?? '';
                        String bTimestamp = b['timestamp']?.toString() ?? '';

                        // Handle timestamp|status format
                        if (aTimestamp.contains('|')) {
                          aTimestamp = aTimestamp.split('|')[0];
                        }
                        if (bTimestamp.contains('|')) {
                          bTimestamp = bTimestamp.split('|')[0];
                        }

                        DateTime aTime, bTime;

                        // Try parsing with different formats
                        try {
                          aTime = DateTime.parse(aTimestamp);
                        } catch (e) {
                          try {
                            aTime = DateTime.fromMillisecondsSinceEpoch(int.parse(aTimestamp));
                          } catch (e2) {
                            aTime = DateTime.now(); // Fallback
                          }
                        }

                        try {
                          bTime = DateTime.parse(bTimestamp);
                        } catch (e) {
                          try {
                            bTime = DateTime.fromMillisecondsSinceEpoch(int.parse(bTimestamp));
                          } catch (e2) {
                            bTime = DateTime.now(); // Fallback
                          }
                        }

                        return bTime.compareTo(aTime); // Descending order
                      } catch (e) {
                        print('Error sorting waterflow reports: $e');
                        return 0; // Keep original order if parsing fails
                      }
                    });

                    return Card(
                      elevation: 4,
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            Text(
                              'Recent Waterflow Reports for Gate $selectedGateId',
                              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                            ),
                            SizedBox(height: 16),
                            if (sortedReports.isNotEmpty) ...[
                              SizedBox(
                                height: 200,
                                child: ListView.builder(
                                  itemCount: sortedReports.length > 5 ? 5 : sortedReports.length,
                                  itemBuilder: (context, index) {
                                    final report = sortedReports[index];
                                    // Parse start time with error handling
                                    DateTime startTime = _parseDateTime(
                                      (report['startTime'] ?? report['timestamp'])?.toString() ?? ''
                                    );

                                    // Parse end time with error handling
                                    DateTime endTime = _parseDateTime(
                                      (report['endTime'] ?? report['timestamp'])?.toString() ?? ''
                                    );

                                    // Get water usage data with proper error handling
                                    final totalWater = (report['totalWater'] as num?)?.toDouble() ?? 0.0;
                                    // Get cubic meters from report or calculate it
                                    final totalWaterCubicMeters =
                                      (report['totalWaterCubicMeters'] as num?)?.toDouble() ?? (totalWater / 1000.0);

                                    // Get session status
                                    final status = report['status']?.toString() ?? 'completed';
                                    final isCompleted = status == 'completed' || status == 'closed';

                                    return Card(
                                      margin: EdgeInsets.symmetric(vertical: 4),
                                      child: ListTile(
                                        title: Row(
                                          children: [
                                            Expanded(
                                              child: Text(
                                                '${totalWater.toStringAsFixed(2)} L',
                                                style: TextStyle(
                                                  fontWeight: FontWeight.bold,
                                                  color: Colors.blue,
                                                ),
                                              ),
                                            ),
                                            Container(
                                              padding: EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                                              decoration: BoxDecoration(
                                                color: isCompleted ? Colors.green.withAlpha(51) : Colors.orange.withAlpha(51),
                                                borderRadius: BorderRadius.circular(4),
                                              ),
                                              child: Text(
                                                status.toUpperCase(),
                                                style: TextStyle(
                                                  fontSize: 10,
                                                  fontWeight: FontWeight.bold,
                                                  color: isCompleted ? Colors.green : Colors.orange,
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                        subtitle: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              '${DateFormat('yyyy-MM-dd HH:mm').format(startTime)} - ${DateFormat('HH:mm').format(endTime)}',
                                              style: TextStyle(fontSize: 12),
                                            ),
                                            Text(
                                              '${totalWaterCubicMeters.toStringAsFixed(3)} m³ | Session: ${_truncateString(report['sessionId']?.toString() ?? 'Unknown', 10)}',
                                              style: TextStyle(fontSize: 12, color: Colors.grey),
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                          ],
                                        ),
                                        trailing: Icon(Icons.water_drop, color: Colors.blue),
                                      ),
                                    );
                                  },
                                ),
                              ),
                            ] else ...[
                              Padding(
                                padding: const EdgeInsets.all(16.0),
                                child: Column(
                                  children: [
                                    Icon(Icons.water_drop, color: Colors.grey.withAlpha(128), size: 48),
                                    SizedBox(height: 16),
                                    Text(
                                      'No waterflow reports available for this gate',
                                      style: TextStyle(fontSize: 16, color: Colors.grey),
                                      textAlign: TextAlign.center,
                                    ),
                                    SizedBox(height: 8),
                                    Text(
                                      'Reports will appear after the gate has been opened and closed',
                                      style: TextStyle(fontSize: 14, color: Colors.grey),
                                      textAlign: TextAlign.center,
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),
                    );
                  }
                  return SizedBox.shrink();
                },
              ),

              SizedBox(height: 16),
              StreamBuilder<DatabaseEvent>(
                stream: _databaseService.listenToGateData(selectedGateId).asBroadcastStream(),
                builder: (context, snapshot) {
                  if (snapshot.hasData && snapshot.data!.snapshot.exists) {
                    final data = Map<String, dynamic>.from(snapshot.data!.snapshot.value as Map);
                    if (data['lastUpdated'] != null) {
                      return Card(
                        elevation: 4,
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.stretch,
                            children: [
                              Text(
                                'Last Update for Gate $selectedGateId',
                                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                              ),
                              SizedBox(height: 8),
                              Text(
                                DateFormat('yyyy-MM-dd HH:mm:ss').format(_parseDateTime(data['lastUpdated']?.toString() ?? '')),
                                style: TextStyle(fontSize: 16),
                              ),
                              if (data['lastOperation'] != null) ...[
                                SizedBox(height: 4),
                                Text(
                                  'Type: ${data['lastOperation']}',
                                  style: TextStyle(fontSize: 14, color: Colors.grey),
                                ),
                              ],
                            ],
                          ),
                        ),
                      );
                    }
                  }
                  return SizedBox.shrink();
                },
              ),
            ],
          ),
        ),
      ),

          // Tab 2: Reports
          SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: StreamBuilder<DatabaseEvent>(
                stream: _databaseService.listenToGateWaterflowReports(selectedGateId).asBroadcastStream(),
                builder: (context, snapshot) {
                  if (snapshot.hasData && snapshot.data!.snapshot.exists) {
                    final reports = Map<String, dynamic>.from(snapshot.data!.snapshot.value as Map);

                    // Sort reports by timestamp (most recent first)
                    List<dynamic> sortedReports = reports.values.toList();

                    // Sort with error handling
                    sortedReports.sort((a, b) {
                      try {
                        String aTimestamp = a['timestamp']?.toString() ?? '';
                        String bTimestamp = b['timestamp']?.toString() ?? '';

                        DateTime aTime = _parseDateTime(aTimestamp);
                        DateTime bTime = _parseDateTime(bTimestamp);

                        return bTime.compareTo(aTime); // Descending order
                      } catch (e) {
                        print('Error sorting waterflow reports: $e');
                        return 0; // Keep original order if parsing fails
                      }
                    });

                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        Card(
                          elevation: 4,
                          child: Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.stretch,
                              children: [
                                Text(
                                  'Waterflow Reports for Gate $selectedGateId',
                                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                                ),
                                SizedBox(height: 16),
                                if (sortedReports.isNotEmpty) ...[
                                  ListView.builder(
                                    shrinkWrap: true,
                                    physics: NeverScrollableScrollPhysics(),
                                    itemCount: sortedReports.length,
                                    itemBuilder: (context, index) {
                                      final report = sortedReports[index];
                                      final startTime = _parseDateTime(report['startTime']?.toString() ?? '');
                                      final endTime = _parseDateTime(report['endTime']?.toString() ?? '');
                                      final totalWater = report['totalWater'] ?? 0.0;

                                      return Card(
                                        margin: EdgeInsets.only(bottom: 8),
                                        child: ListTile(
                                          title: Text('${(totalWater / 1000).toStringAsFixed(2)} L'),
                                          subtitle: Column(
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: [
                                              Text('Session: ${report['sessionId'] ?? 'Unknown'}'),
                                              Text('${DateFormat('yyyy-MM-dd HH:mm').format(startTime)} - ${DateFormat('HH:mm').format(endTime)}'),
                                            ],
                                          ),
                                          trailing: Icon(Icons.water, color: Colors.blue),
                                        ),
                                      );
                                    },
                                  ),
                                ] else ...[
                                  Padding(
                                    padding: const EdgeInsets.all(16.0),
                                    child: Text(
                                      'No waterflow reports available for this gate',
                                      style: TextStyle(fontSize: 16, color: Colors.grey),
                                      textAlign: TextAlign.center,
                                    ),
                                  ),
                                ],
                              ],
                            ),
                          ),
                        ),
                      ],
                    );
                  }
                  return Center(
                    child: Text(
                      'No reports available',
                      style: TextStyle(fontSize: 16, color: Colors.grey),
                    ),
                  );
                },
              ),
            ),
          ),

          // Tab 3: Schedule
          SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: StreamBuilder<DatabaseEvent>(
                stream: _databaseService.listenToGateSchedules(selectedGateId).asBroadcastStream(),
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return Center(child: CircularProgressIndicator());
                  }

                  if (snapshot.hasError) {
                    return Center(
                      child: Text('Error: ${snapshot.error}'),
                    );
                  }

                  if (snapshot.hasData && snapshot.data!.snapshot.exists) {
                    final schedules = Map<String, dynamic>.from(snapshot.data!.snapshot.value as Map);

                    // Sort schedules by date/time
                    List<dynamic> sortedSchedules = schedules.values.toList();
                    sortedSchedules.sort((a, b) {
                      try {
                        final aDateTime = _parseDateTime(a['dateTime']?.toString() ?? '');
                        final bDateTime = _parseDateTime(b['dateTime']?.toString() ?? '');
                        return aDateTime.compareTo(bDateTime);
                      } catch (e) {
                        return 0;
                      }
                    });

                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        Card(
                          elevation: 4,
                          child: Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.stretch,
                              children: [
                                Text(
                                  'Schedules for Gate $selectedGateId',
                                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                                ),
                                SizedBox(height: 16),
                                if (sortedSchedules.isNotEmpty) ...[
                                  ListView.builder(
                                    shrinkWrap: true,
                                    physics: NeverScrollableScrollPhysics(),
                                    itemCount: sortedSchedules.length,
                                    itemBuilder: (context, index) {
                                      final schedule = sortedSchedules[index];
                                      final dateTime = _parseDateTime(schedule['dateTime']?.toString() ?? '');
                                      final action = schedule['action'] ?? 'open';
                                      final isCompleted = schedule['isCompleted'] == true;

                                      return Card(
                                        margin: EdgeInsets.only(bottom: 8),
                                        color: isCompleted ? Colors.grey[100] : Colors.white,
                                        child: ListTile(
                                          leading: Icon(
                                            action == 'open' ? Icons.lock_open : Icons.lock,
                                            color: action == 'open' ? Colors.green : Colors.red,
                                          ),
                                          title: Text(
                                            '${action.toUpperCase()} at ${DateFormat('yyyy-MM-dd HH:mm').format(dateTime)}',
                                            style: TextStyle(
                                              fontWeight: FontWeight.bold,
                                              color: isCompleted ? Colors.grey : Colors.black,
                                            ),
                                          ),
                                          subtitle: Text(
                                            isCompleted ? 'Completed' : 'Scheduled',
                                            style: TextStyle(
                                              color: isCompleted ? Colors.grey : Colors.blue,
                                            ),
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                                ] else ...[
                                  Padding(
                                    padding: const EdgeInsets.all(16.0),
                                    child: Text(
                                      'No schedules available for this gate',
                                      style: TextStyle(fontSize: 16, color: Colors.grey),
                                      textAlign: TextAlign.center,
                                    ),
                                  ),
                                ],
                                SizedBox(height: 16),
                                ElevatedButton.icon(
                                  onPressed: () {
                                    // Navigate to add schedule screen
                                  },
                                  icon: Icon(Icons.add),
                                  label: Text('Add New Schedule'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Theme.of(context).primaryColor,
                                    padding: EdgeInsets.symmetric(vertical: 12),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    );
                  }

                  return Center(
                    child: Text(
                      'No schedules available',
                      style: TextStyle(fontSize: 16, color: Colors.grey),
                    ),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Parse a DateTime from a string with robust error handling
  DateTime _parseDateTime(String dateTimeStr) {
    try {
      // Handle timestamp|status format
      if (dateTimeStr.contains('|')) {
        dateTimeStr = dateTimeStr.split('|')[0];
      }
      
      // Try standard ISO format first
      return DateTime.parse(dateTimeStr);
    } catch (e) {
      try {
        // Try Unix timestamp (milliseconds since epoch)
        return DateTime.fromMillisecondsSinceEpoch(int.parse(dateTimeStr));
      } catch (e2) {
        try {
          // Try Unix timestamp (seconds since epoch)
          return DateTime.fromMillisecondsSinceEpoch(int.parse(dateTimeStr) * 1000);
        } catch (e3) {
          // If all else fails, return current time
          print('Error parsing date time: $dateTimeStr - $e3');
          return DateTime.now();
        }
      }
    }
  }

  // Truncate string to specified length
  String _truncateString(String text, int maxLength) {
    if (text.length <= maxLength) {
      return text;
    }
    return '${text.substring(0, maxLength)}...';
  }
}