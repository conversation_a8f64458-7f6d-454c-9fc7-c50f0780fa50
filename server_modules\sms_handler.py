#!/usr/bin/env python3
"""
SMS Handler module for FarmFlow Orange Pi Server
Handles all SMS communication using Gammu and direct serial connection
"""

import time
import logging
import serial
import serial.tools.list_ports
try:
    import gammu
    GAMMU_AVAILABLE = True
except ImportError:
    GAMMU_AVAILABLE = False
    print("Warning: Gammu not available. SMS functionality will be limited.")

try:
    from .config import (
        SERIAL_PORT, SERIAL_BAUD_RATE, SERIAL_TIMEOUT,
        SMS_CONFIG, ERROR_MESSAGES, SUCCESS_MESSAGES, IS_LINUX,
        PHONE_TO_GATE
    )
    from .message_parser import MessageParser
    from .firebase_operations import firebase_manager
except ImportError:
    from config import (
        SERIAL_PORT, SERIAL_BAUD_RATE, SERIAL_TIMEOUT,
        SMS_CONFIG, ERROR_MESSAGES, SUCCESS_MESSAGES, IS_LINUX,
        PHONE_TO_GATE
    )
    from message_parser import MessageParser
    from firebase_operations import firebase_manager

# Initialize message parser
message_parser = MessageParser()

def normalize_phone_number(phone_number):
    """Normalize phone number to standard format"""
    if not phone_number:
        return phone_number
    
    # Remove all non-digit characters
    digits_only = ''.join(filter(str.isdigit, phone_number))
    
    # Handle Philippine numbers
    if digits_only.startswith('63'):
        return f"+{digits_only}"
    elif digits_only.startswith('9') and len(digits_only) == 10:
        return f"+63{digits_only}"
    elif digits_only.startswith('0') and len(digits_only) == 11:
        return f"+63{digits_only[1:]}"
    else:
        return f"+{digits_only}"

# Set up logging
logger = logging.getLogger("SMSHandler")

# Try to import required libraries
try:
    import gammu
    GAMMU_AVAILABLE = True
    logger.info("Gammu library found. Using real SMS functionality.")
except ImportError:
    GAMMU_AVAILABLE = False
    logger.warning("Gammu not installed. Run: pip install python-gammu")
    logger.warning("Falling back to mock SMS functionality.")

try:
    import serial
    import serial.tools.list_ports
    SERIAL_AVAILABLE = True
    logger.info("pyserial library found. Using for serial communication.")
except ImportError:
    SERIAL_AVAILABLE = False
    logger.warning("pyserial not installed. Run: pip install pyserial")
    logger.warning("Serial port auto-detection and direct serial communication will be limited.")


class SMSHandler:
    """Handles SMS communication using Gammu and direct serial connection"""
    
    def __init__(self):
        self.sm = None
        self.serial = None
        self.is_initialized = False
        
        # Initialize serial connection
        self._init_serial_connection()
        
        # Try to initialize using Gammu if available
        if GAMMU_AVAILABLE:
            self._init_gammu()
        else:
            logger.warning("Gammu not available, using direct serial communication if possible")
        
        # Log the initialization status
        if self.sm:
            logger.info("SMS handler initialized using Gammu")
            self.is_initialized = True
        elif self.serial:
            logger.info("SMS handler initialized using direct serial connection")
            self.is_initialized = True
        else:
            logger.warning("SMS handler not initialized - using mock functionality")
    
    def _init_serial_connection(self):
        """Initialize direct serial connection to SIM800L module"""
        try:
            if SERIAL_AVAILABLE:
                self.serial = serial.Serial(SERIAL_PORT, SERIAL_BAUD_RATE, timeout=SERIAL_TIMEOUT)
                logger.info(f"Successfully connected to SIM800L module at {SERIAL_PORT}")
        except Exception as serial_err:
            logger.error(f"Failed to connect to SIM800L module: {serial_err}")
            self.serial = None
    
    def _init_gammu(self):
        """Initialize Gammu for SMS handling"""
        try:
            self.sm = gammu.StateMachine()
            
            # Try different configurations
            configs = [
                {'Device': SERIAL_PORT, 'Connection': 'at115200'},
                {'Device': SERIAL_PORT, 'Connection': 'at19200'},
                {'Device': SERIAL_PORT, 'Connection': 'at9600'},
            ]
            
            for config in configs:
                try:
                    logger.info(f"Trying Gammu config: {config}")
                    self.sm.SetConfig(0, config)
                    self.sm.Init()
                    
                    # Test the connection
                    network_info = self.sm.GetNetworkInfo()
                    logger.info(f"Connected to network: {network_info}")
                    
                    # Get signal quality
                    signal_quality = self.sm.GetSignalQuality()
                    logger.info(f"Signal quality: {signal_quality}")
                    
                    logger.info("SMS module initialized successfully with Gammu")
                    return
                    
                except Exception as config_err:
                    logger.warning(f"Config {config} failed: {config_err}")
                    continue
            
            # If we get here, all configs failed
            logger.error("All Gammu configurations failed")
            self.sm = None
            
        except Exception as e:
            logger.error(f"Failed to initialize SMS module: {e}")
            self.sm = None
    
    def send_sms(self, phone_number, message):
        """Send SMS message with detailed logging"""
        try:
            logger.info(f"[SMS] Attempting to send SMS to {phone_number}: '{message}'")
            
            if self.sm:
                return self._send_sms_gammu(phone_number, message)
            elif self.serial:
                return self._send_sms_direct(phone_number, message)
            else:
                return self._send_sms_mock(phone_number, message)
            
        except Exception as e:
            logger.error(f"[SMS] Failed to send SMS to {phone_number}: {e}")
            return False
    
    def _send_sms_gammu(self, phone_number, message):
        """Send SMS using Gammu with detailed logging"""
        try:
            logger.info(f"[SMS] Using Gammu to send SMS to {phone_number}")
            
            sms_info = {
                'Text': message,
                'SMSC': {'Location': 1},
                'Number': phone_number,
            }
            
            self.sm.SendSMS(sms_info)
            logger.info(f"[SMS] ✅ Gammu SMS sent successfully to {phone_number}")
            return True
            
        except Exception as e:
            logger.error(f"[SMS] Gammu SMS failed to {phone_number}: {e}")
            return False
    
    def _send_sms_direct(self, phone_number, message):
        """Send SMS using direct serial connection with detailed logging"""
        try:
            logger.info(f"[SMS] Using direct serial to send SMS to {phone_number}")
            
            # Set SMS text mode
            self.serial.write(b'AT+CMGF=1\r\n')
            time.sleep(1)
            response = self.serial.read(100).decode('utf-8', errors='ignore')
            logger.debug(f"[SMS] Text mode response: {response}")
            
            if "OK" not in response:
                logger.error(f"[SMS] Failed to set text mode")
                return False
            
            # Set recipient
            cmd = f'AT+CMGS="{phone_number}"\r\n'
            self.serial.write(cmd.encode())
            time.sleep(1)
            response = self.serial.read(100).decode('utf-8', errors='ignore')
            logger.debug(f"[SMS] Recipient response: {response}")
            
            if ">" not in response:
                logger.error(f"[SMS] Failed to set recipient")
                return False
            
            # Send message
            self.serial.write(f"{message}\x1A".encode())
            time.sleep(3)
            response = self.serial.read(200).decode('utf-8', errors='ignore')
            logger.debug(f"[SMS] Send response: {response}")
            
            if "+CMGS:" in response or "OK" in response:
                logger.info(f"[SMS] ✅ Direct serial SMS sent successfully to {phone_number}")
                return True
            else:
                logger.error(f"[SMS] Direct serial SMS failed to {phone_number}")
                return False
            
        except Exception as e:
            logger.error(f"[SMS] Direct serial SMS failed to {phone_number}: {e}")
            return False
    
    def _send_sms_mock(self, phone_number, message):
        """Mock SMS sending for testing"""
        logger.info(f"[SMS] 🧪 MOCK: Would send SMS to {phone_number}: '{message}'")
        return True
    
    def read_all_sms(self):
        """Read all SMS messages"""
        if self.sm:
            return self._read_sms_gammu()
        elif self.serial:
            return self._read_sms_direct()
        else:
            return self._read_sms_mock()
    
    def _read_sms_gammu(self):
        """Read SMS messages using Gammu"""
        try:
            # Get SMS status
            status = self.sm.GetSMSStatus()
            logger.debug(f"SMS Status: {status}")
            
            if status['SIMUsed'] == 0 and status['PhoneUsed'] == 0:
                logger.debug("No SMS messages found")
                return []
            
            # Read all SMS messages
            sms_list = []
            start = True
            
            while True:
                try:
                    if start:
                        sms = self.sm.GetNextSMS(Start=True, Folder=0)
                        start = False
                    else:
                        sms = self.sm.GetNextSMS(Location=sms[0]['Location'], Folder=0)
                    
                    sms_list.extend(sms)
                    
                except gammu.ERR_EMPTY:
                    break
                except Exception as e:
                    logger.error(f"Error reading SMS: {e}")
                    break
            
            logger.info(f"Read {len(sms_list)} SMS messages")
            return sms_list
            
        except Exception as e:
            logger.error(f"Failed to read SMS messages: {e}")
            return []
    
    def _read_sms_direct(self):
        """Read SMS messages using direct serial connection"""
        try:
            logger.debug("Reading SMS via direct serial connection")
            
            # AT command to list all SMS
            self.serial.write(b'AT+CMGL="ALL"\r\n')
            time.sleep(2)
            
            response = self.serial.read(1000).decode('utf-8', errors='ignore')
            logger.debug(f"SMS List Response: {response}")
            
            # Parse SMS messages from response
            sms_list = self._parse_sms_response(response)
            logger.info(f"Read {len(sms_list)} SMS messages via direct serial")
            return sms_list
            
        except Exception as e:
            logger.error(f"Failed to read SMS via direct serial: {e}")
            return []
    
    def _read_sms_mock(self):
        """Mock SMS reading for testing"""
        logger.debug("Mock SMS reading - no messages")
        return []
    
    def _parse_sms_response(self, response):
        """Parse SMS response from AT command"""
        sms_list = []
        lines = response.split('\n')
        
        i = 0
        while i < len(lines):
            line = lines[i].strip()
            if line.startswith('+CMGL:'):
                try:
                    # Parse SMS header
                    parts = line.split(',')
                    if len(parts) >= 3:
                        location = int(parts[0].split(':')[1].strip())
                        sender = parts[2].strip('"')
                        
                        # Get message content from next line
                        if i + 1 < len(lines):
                            message = lines[i + 1].strip()
                            
                            sms_list.append({
                                'Location': location,
                                'Number': sender,
                                'Text': message
                            })
                except Exception as e:
                    logger.error(f"Error parsing SMS line: {line}, error: {e}")
            i += 1
        
        return sms_list
    
    def delete_sms(self, location):
        """Delete SMS message at specified location"""
        if self.sm:
            return self._delete_sms_gammu(location)
        elif self.serial:
            return self._delete_sms_direct(location)
        else:
            return self._delete_sms_mock(location)
    
    def _delete_sms_gammu(self, location):
        """Delete SMS using Gammu"""
        try:
            self.sm.DeleteSMS(Folder=0, Location=location)
            logger.debug(f"Deleted SMS at location {location}")
            return True
        except Exception as e:
            logger.error(f"Failed to delete SMS at location {location}: {e}")
            return False
    
    def _delete_sms_direct(self, location):
        """Delete SMS using direct serial connection"""
        try:
            command = f'AT+CMGD={location}\r\n'
            self.serial.write(command.encode())
            time.sleep(0.5)
            response = self.serial.read(100).decode('utf-8', errors='ignore')
            logger.debug(f"Delete SMS response: {response}")
            return True
        except Exception as e:
            logger.error(f"Failed to delete SMS at location {location}: {e}")
            return False
    
    def _delete_sms_mock(self, location):
        """Mock SMS deletion"""
        logger.debug(f"Mock: Would delete SMS at location {location}")
        return True
    
    def get_signal_quality(self):
        """Get signal quality information"""
        if self.sm:
            try:
                return self.sm.GetSignalQuality()
            except Exception as e:
                logger.error(f"Failed to get signal quality: {e}")
                return None
        return None
    
    def get_network_info(self):
        """Get network information"""
        if self.sm:
            try:
                return self.sm.GetNetworkInfo()
            except Exception as e:
                logger.error(f"Failed to get network info: {e}")
                return None
        return None
    
    def is_ready(self):
        """Check if SMS handler is ready"""
        return self.is_initialized
    
    def close(self):
        """Close SMS handler connections"""
        if self.serial and self.serial.is_open:
            self.serial.close()
            logger.info("Serial connection closed")
        
        if self.sm:
            try:
                self.sm.Terminate()
                logger.info("Gammu connection terminated")
            except:
                pass

    def process_incoming_sms(self, message: str, sender: str) -> bool:
        """Process incoming SMS with better error handling"""
        try:
            # Normalize sender phone number
            sender = normalize_phone_number(sender)
            
            # Find which gate this message is from
            gate_id = PHONE_TO_GATE.get(sender, 'G001')
            
            print(f"📱 Processing SMS from {sender} (Gate: {gate_id})")
            print(f"📝 Message: {message}")
            
            # Parse the message
            parsed = message_parser.parse_sms_message(message, sender)
            parsed['gate_id'] = gate_id  # Override with detected gate
            
            # Handle different message types
            if parsed['type'] == 'OPEN' or 'OPEN' in message:
                return self._handle_gate_status(gate_id, 'OPEN', parsed)
            elif parsed['type'] == 'CLOSE' or 'CLOSE' in message:
                return self._handle_gate_status(gate_id, 'CLOSED', parsed)
            elif parsed['type'] == 'STATUS':
                return self._handle_status_request(gate_id, parsed)
            else:
                # Log unknown message but don't fail
                print(f"⚠️ Unknown message type: {parsed['type']}")
                return True
            
        except Exception as e:
            print(f"❌ SMS processing error: {e}")
            return False

    def _handle_gate_status(self, gate_id: str, status: str, parsed: dict) -> bool:
        """Handle gate status update"""
        try:
            # Update Firebase
            success = firebase_manager.update_gate_status(gate_id, status)
            
            if success:
                print(f"✅ Updated {gate_id} status to {status}")
            else:
                print(f"❌ Failed to update {gate_id} status")
            
            return success
            
        except Exception as e:
            print(f"❌ Gate status update error: {e}")
            return False

    def _get_gate_by_phone(self, phone_number):
        """Get gate ID from database by phone number"""
        try:
            # Normalize phone number
            phone_number = self._normalize_phone_number(phone_number)
            
            # Search database for gate with this phone number
            gates_data = firebase_manager.db.child("farmflow/gates").get()
            if gates_data.each():
                for gate_item in gates_data.each():
                    gate_data = gate_item.val()
                    if gate_data and gate_data.get("phoneNumber") == phone_number:
                        logger.info(f"Found gate {gate_item.key()} for phone {phone_number}")
                        return gate_item.key()
            
            logger.warning(f"No gate found for phone number {phone_number}")
            return None
            
        except Exception as e:
            logger.error(f"Error finding gate by phone {phone_number}: {e}")
            return None

    def _normalize_phone_number(self, phone: str) -> str:
        """Normalize phone number format"""
        if not phone:
            return phone
        
        # Remove spaces and special characters except +
        phone = ''.join(c for c in phone if c.isdigit() or c == '+')
        
        # Ensure it starts with +63 for Philippines
        if phone.startswith('63') and not phone.startswith('+63'):
            phone = '+' + phone
        elif phone.startswith('9') and len(phone) == 10:
            phone = '+63' + phone
        
        return phone







