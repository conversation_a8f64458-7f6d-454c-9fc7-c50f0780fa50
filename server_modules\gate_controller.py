#!/usr/bin/env python3
"""
Gate Controller module for FarmFlow Orange Pi Server
Handles gate command sending, status management, and timeout handling
"""

import datetime
import logging
import time
try:
    from .config import GATE_CONFIG, get_gate_phone, validate_gate_id, SUCCESS_MESSAGES, ERROR_MESSAGES
    from .firebase_operations import firebase_manager
except ImportError:
    from config import GATE_CONFIG, get_gate_phone, validate_gate_id, SUCCESS_MESSAGES, ERROR_MESSAGES
    from firebase_operations import firebase_manager

# Set up logging
logger = logging.getLogger("GateController")


class GateController:
    """Manages gate operations, commands, and status tracking"""
    
    def __init__(self, sms_handler):
        self.sms_handler = sms_handler
        self.firebase = firebase_manager
        self.pending_operations = {}  # Track pending operations
        self.processed_commands = set()  # Track processed command IDs to prevent duplicates
        self.last_command_check = 0  # Timestamp of last command check
        
    def send_gate_command(self, gate_id, command):
        """
        Send command to gate and set pending status
        
        Args:
            gate_id (str): Gate ID (e.g., "G001")
            command (str): Command to send ("OPEN" or "CLOSE")
            
        Returns:
            bool: True if command was sent successfully
        """
        try:
            if not validate_gate_id(gate_id):
                logger.error(f"[GATE CMD] Invalid gate ID: {gate_id}")
                return False
            
            if command not in ["OPEN", "CLOSE", "LOCATION"]:
                logger.error(f"[GATE CMD] Invalid command: {command}")
                return False
            
            # Get gate phone number from database first, fallback to config
            phone_number = self._get_gate_phone_from_database(gate_id)
            if not phone_number:
                # Fallback to config file
                phone_number = get_gate_phone(gate_id)
                if not phone_number:
                    logger.error(f"[GATE CMD] No phone number found for gate {gate_id}")
                    return False
            
            logger.info(f"[GATE CMD] Sending SMS '{command}' to gate {gate_id} at {phone_number}")
            
            # Send SMS command
            sms_result = self.sms_handler.send_sms(phone_number, command)
            
            if not sms_result:
                logger.error(f"[GATE CMD] ❌ Failed to send SMS to {phone_number}")
                return False
            
            logger.info(f"[GATE CMD] ✅ SMS sent successfully to {phone_number}")
            
            # Update gate status in Firebase
            current_time = datetime.datetime.now().isoformat()
            
            if command == "LOCATION":
                # For location requests, just update the request status
                gate_update = {
                    "lastLocationRequest": current_time,
                    "locationRequestStatus": "pending",
                    "lastCommand": command,
                    "lastCommandTime": current_time,
                    "commandSent": True
                }
            else:
                # For OPEN/CLOSE commands, update gate status to pending state
                pending_status = f"pending_{command.lower()}"
                logger.info(f"[GATE CMD] Setting gate {gate_id} to pending status: {pending_status}")
                gate_update = {
                    "gateStatus": pending_status,
                    "operationStatus": "in_progress",
                    "lastUpdated": current_time,
                    "lastOperation": "manual",
                    "lastOperationTime": current_time,
                    "lastCommand": command,
                    "lastCommandTime": current_time,
                    "pendingConfirmation": True,
                    "confirmationTimeout": current_time,
                    "expectedStatus": command.lower(),
                    "commandSent": True,
                    "awaitingConfirmation": True
                }
            
            firebase_success = self.firebase.update_gate_data(gate_id, gate_update)
            if not firebase_success:
                logger.error(f"[GATE CMD] Failed to update gate status in Firebase")
                return False
            
            # Track this operation for timeout handling (only for OPEN/CLOSE commands)
            if command in ["OPEN", "CLOSE"]:
                self.pending_operations[gate_id] = {
                    "command": command,
                    "start_time": datetime.datetime.now(),
                    "expected_status": command.lower()
                }

            logger.info(f"[GATE CMD] ✅ Command '{command}' sent successfully to gate {gate_id}")
            return True
            
        except Exception as e:
            logger.error(f"[GATE CMD] Error sending command to gate {gate_id}: {e}")
            return False
    
    def check_pending_operations_timeout(self):
        """Check for pending gate operations that have timed out and revert them"""
        try:
            logger.debug("[TIMEOUT] Checking for timed out gate operations")
            
            # Get all gates
            all_gates = self.firebase.get_all_gates()
            if not all_gates:
                return
            
            current_time = datetime.datetime.now()
            timeout_seconds = GATE_CONFIG["operation_timeout"]
            
            for gate_id, gate_data in all_gates.items():
                # Check if gate has a pending operation
                gate_status = gate_data.get("gateStatus", "")
                operation_status = gate_data.get("operationStatus", "")
                pending_confirmation = gate_data.get("pendingConfirmation", False)
                confirmation_timeout = gate_data.get("confirmationTimeout")
                
                if (gate_status.startswith("pending_") and 
                    operation_status == "in_progress" and 
                    pending_confirmation and 
                    confirmation_timeout):
                    
                    try:
                        # Parse the timeout timestamp
                        timeout_time = datetime.datetime.fromisoformat(confirmation_timeout)
                        time_elapsed = (current_time - timeout_time).total_seconds()
                        
                        if time_elapsed > timeout_seconds:
                            # Operation has timed out
                            logger.warning(f"[TIMEOUT] Gate {gate_id} operation timed out after {time_elapsed:.1f} seconds")
                            
                            # Revert to previous status or set to error
                            expected_status = gate_data.get("expectedStatus", "")
                            if expected_status == "open":
                                reverted_status = "closed"  # Assume it was closed before
                            else:
                                reverted_status = "open"   # Assume it was open before
                            
                            # Update gate status to timeout/error state
                            timeout_update = {
                                "gateStatus": reverted_status,
                                "operationStatus": "timeout",
                                "pendingConfirmation": False,
                                "lastUpdated": current_time.isoformat(),
                                "timeoutOccurred": True,
                                "timeoutTime": current_time.isoformat(),
                                "errorMessage": f"Operation timed out after {timeout_seconds} seconds - no magnet sensor confirmation received"
                            }
                            
                            self.firebase.update_gate_data(gate_id, timeout_update)
                            logger.info(f"[TIMEOUT] Reverted gate {gate_id} status to {reverted_status} due to timeout")
                            
                            # Remove from pending operations tracking
                            if gate_id in self.pending_operations:
                                del self.pending_operations[gate_id]
                    
                    except Exception as timeout_err:
                        logger.error(f"[TIMEOUT] Error processing timeout for gate {gate_id}: {timeout_err}")
        
        except Exception as e:
            logger.error(f"[TIMEOUT] Error checking pending operations timeout: {e}")
    
    def handle_gate_status_change(self, gate_id, new_status):
        """
        Handle gate status changes for session management and database updates

        Args:
            gate_id (str): Gate ID
            new_status (str): New gate status
        """
        try:
            logger.info(f"[STATUS] Handling status change for gate {gate_id}: {new_status}")

            # First, update the gate status in the database
            self._update_gate_status_in_database(gate_id, new_status)

            # Then handle session management
            gate_data = self.firebase.get_gate_data(gate_id)
            if not gate_data:
                logger.warning(f"[SESSION] No gate data found for {gate_id}")
                return

            current_session_id = gate_data.get("currentSessionId")

            if new_status == "open":
                if not current_session_id:
                    # Start new session
                    session_id = self._create_new_session(gate_id)
                    logger.info(f"[SESSION] Started new session {session_id} for gate {gate_id}")
                else:
                    logger.info(f"[SESSION] Gate {gate_id} already has active session {current_session_id}")

            elif new_status == "closed":
                if current_session_id:
                    # Finalize current session
                    self._finalize_session(gate_id, current_session_id)
                    logger.info(f"[SESSION] Finalized session {current_session_id} for gate {gate_id}")
                else:
                    logger.info(f"[SESSION] Gate {gate_id} closed but no active session found")

        except Exception as e:
            logger.error(f"[STATUS] Error handling status change for gate {gate_id}: {e}")

    def _update_gate_status_in_database(self, gate_id, new_status):
        """Update gate status directly in the database"""
        try:
            logger.info(f"[STATUS] Updating gate {gate_id} status to '{new_status}' in database")

            # Update gate status
            gate_path = f"farmflow/gates/{gate_id}"
            status_update = {
                "gateStatus": new_status,
                "lastUpdated": datetime.datetime.now().isoformat()
            }

            # Update the gate status in Firebase
            self.firebase.db.child(gate_path).update(status_update)

            logger.info(f"[STATUS] ✅ Successfully updated gate {gate_id} status to '{new_status}'")

            # Also clear any pending commands for this gate since status was confirmed
            self._clear_pending_commands_for_gate(gate_id)

        except Exception as e:
            logger.error(f"[STATUS] ❌ Failed to update gate {gate_id} status: {e}")

    def _clear_pending_commands_for_gate(self, gate_id):
        """Clear pending commands for a gate after status confirmation"""
        try:
            logger.debug(f"[STATUS] Clearing pending commands for gate {gate_id}")

            # Get all pending commands
            commands_data = self.firebase.db.child("farmflow/gate_commands").get()
            if commands_data.each():
                for cmd_item in commands_data.each():
                    cmd_data = cmd_item.val()
                    if cmd_data and cmd_data.get("gate_id") == gate_id:
                        # Delete this command as it's been confirmed
                        self.firebase.db.child(f"farmflow/gate_commands/{cmd_item.key()}").remove()
                        logger.info(f"[STATUS] Cleared pending command {cmd_item.key()} for gate {gate_id}")

        except Exception as e:
            logger.warning(f"[STATUS] Error clearing pending commands for gate {gate_id}: {e}")
    
    def _create_new_session(self, gate_id):
        """Create a new waterflow session"""
        try:
            session_id = f"{gate_id}_{int(time.time())}"
            current_time = datetime.datetime.now().isoformat()
            
            session_data = {
                "sessionId": session_id,
                "gateId": gate_id,
                "startTime": current_time,
                "status": "active",
                "totalWaterFlow": 0.0,
                "dataPoints": 0,
                "lastUpdate": current_time
            }
            
            # Store session data
            self.firebase.update_waterflow_session(gate_id, session_id, session_data)
            
            # Update gate with current session ID
            self.firebase.update_gate_data(gate_id, {
                "currentSessionId": session_id,
                "sessionStartTime": current_time
            })
            
            return session_id
        
        except Exception as e:
            logger.error(f"[SESSION] Error creating new session for gate {gate_id}: {e}")
            return None
    
    def _finalize_session(self, gate_id, session_id):
        """Finalize a waterflow session"""
        try:
            current_time = datetime.datetime.now().isoformat()
            
            # Get current session data
            sessions = self.firebase.get_waterflow_sessions(gate_id)
            current_session = None
            
            for session in sessions:
                if session.get("sessionId") == session_id:
                    current_session = session
                    break
            
            if current_session:
                # Calculate session duration
                start_time = datetime.datetime.fromisoformat(current_session.get("startTime", current_time))
                end_time = datetime.datetime.now()
                duration = (end_time - start_time).total_seconds()
                
                # Update session with final data
                session_update = {
                    "endTime": current_time,
                    "status": "completed",
                    "duration": duration,
                    "lastUpdate": current_time
                }
                
                self.firebase.update_waterflow_session(gate_id, session_id, session_update)
                
                # Clear current session from gate
                self.firebase.update_gate_data(gate_id, {
                    "currentSessionId": None,
                    "sessionStartTime": None,
                    "lastSessionEndTime": current_time
                })
                
                logger.info(f"[SESSION] Session {session_id} completed for gate {gate_id}, duration: {duration:.1f}s")
            else:
                logger.warning(f"[SESSION] Session {session_id} not found for gate {gate_id}")
        
        except Exception as e:
            logger.error(f"[SESSION] Error finalizing session {session_id} for gate {gate_id}: {e}")
    
    def process_firebase_commands(self):
        """Process pending commands from Firebase and remove them only after successful SMS sending"""
        try:
            logger.debug("[CMD] Checking for pending commands...")
            
            # Get commands from Firebase using the correct path
            commands_path = "farmflow/gate_commands"
            commands_ref = self.firebase.db.child(commands_path)
            commands_data = commands_ref.get()
            
            if not commands_data.val():
                logger.debug("[CMD] No commands found in database")
                return
            
            commands = commands_data.val()
            logger.info(f"[CMD] Found {len(commands)} commands to process at path: {commands_path}")
            
            for command_id, command_data in commands.items():
                try:
                    logger.info(f"[CMD] Processing command {command_id}: {command_data}")
                    
                    gate_id = command_data.get('gateId')
                    action = command_data.get('action', '').upper()
                    
                    if not gate_id or not action:
                        logger.warning(f"[CMD] Invalid command data: {command_data}")
                        # Remove invalid command immediately
                        self._delete_command_from_database(command_id)
                        continue
                    
                    # STEP 1: Send the command to the gate FIRST
                    logger.info(f"[CMD] � Sending {action} command to gate {gate_id}")
                    success = self.send_gate_command(gate_id, action)
                    
                    if success:
                        logger.info(f"[CMD] ✅ SMS sent successfully to gate {gate_id}")
                        
                        # STEP 2: Only delete command AFTER successful SMS sending
                        logger.info(f"[CMD] �️ Removing command {command_id} from database AFTER successful SMS")
                        delete_success = self._delete_command_from_database(command_id)
                        
                        if delete_success:
                            logger.info(f"[CMD] ✅ Command {command_id} successfully processed and removed")
                        else:
                            logger.error(f"[CMD] ❌ Failed to delete command {command_id} after successful SMS")
                    else:
                        logger.error(f"[CMD] ❌ Failed to send SMS to gate {gate_id}, keeping command in database")
                        # Don't delete command if SMS failed - it will be retried
                    
                except Exception as cmd_err:
                    logger.error(f"[CMD] Error processing command {command_id}: {cmd_err}")
                    # Don't delete command on error - it will be retried
        
        except Exception as e:
            logger.error(f"[CMD] Error processing Firebase commands: {e}")

    def _delete_command_from_database(self, command_id):
        """Delete command from Firebase database"""
        try:
            commands_path = "farmflow/gate_commands"
            command_ref = self.firebase.db.child(commands_path).child(command_id)
            
            logger.info(f"[CMD] 🗑️ Deleting command {command_id} from path: {commands_path}/{command_id}")
            
            # Delete the command
            command_ref.remove()
            
            # Verify deletion
            verification = command_ref.get()
            if verification.val() is None:
                logger.info(f"[CMD] ✅ VERIFIED: Command {command_id} successfully deleted from database")
                return True
            else:
                logger.error(f"[CMD] ❌ FAILED: Command {command_id} still exists in database after deletion")
                return False
            
        except Exception as e:
            logger.error(f"[CMD] Error deleting command {command_id}: {e}")
            return False

    def _process_command_by_type(self, gate_id, action, command_type, source, command_data):
        """Process command based on its type and source"""
        try:
            # Try to get phone number from command data first (preferred)
            phone_number = command_data.get('phoneNumber')
            if phone_number:
                logger.info(f"[CMD] Using phone number from command for gate {gate_id}: {phone_number}")
            else:
                # Fallback: Get gate phone number from database
                phone_number = self._get_gate_phone_from_database(gate_id)
                if not phone_number:
                    # Final fallback to config file
                    phone_number = get_gate_phone(gate_id)
                    if not phone_number:
                        logger.error(f"[CMD] No phone number found for gate {gate_id} in command, database, or config")
                        return False
                    else:
                        logger.info(f"[CMD] Using phone number from config for gate {gate_id}: {phone_number}")
                else:
                    logger.info(f"[CMD] Using phone number from database for gate {gate_id}: {phone_number}")

            # Process based on command type
            if command_type == "manual":
                logger.info(f"[CMD] Processing MANUAL command: {action} for gate {gate_id}")
                return self._process_manual_command(gate_id, action, phone_number, command_data)

            elif command_type == "location_request":
                logger.info(f"[CMD] Processing LOCATION REQUEST for gate {gate_id}")
                return self._process_location_command(gate_id, phone_number, command_data)

            elif command_type == "scheduled":
                logger.info(f"[CMD] Processing SCHEDULED command: {action} for gate {gate_id}")
                return self._process_scheduled_command(gate_id, action, phone_number, command_data)

            else:
                logger.warning(f"[CMD] Unknown command type: {command_type}")
                # Fallback to basic command processing
                return self.send_gate_command(gate_id, action)

        except Exception as e:
            logger.error(f"[CMD] Error processing command by type: {e}")
            return False

    def _process_manual_command(self, gate_id, action, phone_number, command_data):
        """Process manual gate control command (OPEN/CLOSE)"""
        try:
            logger.info(f"[MANUAL] Sending {action} command to gate {gate_id} at {phone_number}")

            # Send SMS command
            result = self.sms_handler.send_sms(phone_number, action)

            if result:
                # Update gate status for manual commands
                current_time = datetime.datetime.now().isoformat()
                new_status = "open" if action == "OPEN" else "closed"

                gate_update = {
                    "gateStatus": new_status,
                    "lastUpdated": current_time,
                    "lastOperation": "manual",
                    "lastOperationTime": current_time,
                    "lastCommand": action,
                    "lastCommandTime": current_time,
                    "commandSource": command_data.get("source", "app"),
                    "operationStatus": "completed"
                }

                success = self.firebase.update_gate_data(gate_id, gate_update)
                if success:
                    logger.info(f"[MANUAL] Gate {gate_id} status updated to {new_status}")
                    return True
                else:
                    logger.error(f"[MANUAL] Failed to update gate status in database")
                    return False
            else:
                logger.error(f"[MANUAL] Failed to send SMS command to gate {gate_id}")
                return False

        except Exception as e:
            logger.error(f"[MANUAL] Error processing manual command: {e}")
            return False

    def _process_location_command(self, gate_id, phone_number, command_data):
        """Process location request command"""
        try:
            logger.info(f"[LOCATION] Requesting location from gate {gate_id} at {phone_number}")

            # Send LOCATION SMS command
            result = self.sms_handler.send_sms(phone_number, "LOCATION")

            if result:
                # Update gate with location request info
                current_time = datetime.datetime.now().isoformat()

                gate_update = {
                    "lastLocationRequest": current_time,
                    "locationRequestStatus": "pending",
                    "lastCommand": "LOCATION",
                    "lastCommandTime": current_time,
                    "commandSource": command_data.get("source", "app")
                }

                success = self.firebase.update_gate_data(gate_id, gate_update)
                if success:
                    logger.info(f"[LOCATION] Location request sent to gate {gate_id}")
                    return True
                else:
                    logger.error(f"[LOCATION] Failed to update gate location request status")
                    return False
            else:
                logger.error(f"[LOCATION] Failed to send location request to gate {gate_id}")
                return False

        except Exception as e:
            logger.error(f"[LOCATION] Error processing location command: {e}")
            return False

    def _process_scheduled_command(self, gate_id, action, phone_number, command_data):
        """Process scheduled gate command"""
        try:
            schedule_id = command_data.get("scheduleId", "unknown")
            logger.info(f"[SCHEDULED] Executing scheduled {action} for gate {gate_id} (schedule: {schedule_id})")

            # Send SMS command
            result = self.sms_handler.send_sms(phone_number, action)

            if result:
                # Update gate status for scheduled commands
                current_time = datetime.datetime.now().isoformat()
                new_status = "open" if action == "OPEN" else "closed"

                gate_update = {
                    "gateStatus": new_status,
                    "lastUpdated": current_time,
                    "lastOperation": "scheduled",
                    "lastOperationTime": current_time,
                    "lastCommand": action,
                    "lastCommandTime": current_time,
                    "lastScheduleId": schedule_id,
                    "commandSource": "scheduler",
                    "operationStatus": "completed"
                }

                success = self.firebase.update_gate_data(gate_id, gate_update)
                if success:
                    logger.info(f"[SCHEDULED] Gate {gate_id} status updated to {new_status} (schedule: {schedule_id})")
                    return True
                else:
                    logger.error(f"[SCHEDULED] Failed to update gate status in database")
                    return False
            else:
                logger.error(f"[SCHEDULED] Failed to send scheduled SMS command to gate {gate_id}")
                return False

        except Exception as e:
            logger.error(f"[SCHEDULED] Error processing scheduled command: {e}")
            return False

    def _get_gate_phone_from_database(self, gate_id):
        """Get gate phone number from database"""
        try:
            gate_data = self.firebase.get_gate_data(gate_id)
            if gate_data and 'phoneNumber' in gate_data:
                phone = gate_data['phoneNumber']
                logger.info(f"[GATE CMD] Found phone number for gate {gate_id}: {phone}")
                return phone
            else:
                logger.warning(f"[GATE CMD] No phone number found in database for gate {gate_id}")
                return None
        except Exception as e:
            logger.error(f"[GATE CMD] Error getting phone number for gate {gate_id}: {e}")
            return None

    def get_gate_status(self, gate_id):
        """Get current gate status"""
        gate_data = self.firebase.get_gate_data(gate_id)
        if gate_data:
            return gate_data.get("gateStatus", "unknown")
        return "unknown"
    
    def is_gate_pending(self, gate_id):
        """Check if gate has a pending operation"""
        gate_data = self.firebase.get_gate_data(gate_id)
        if gate_data:
            status = gate_data.get("gateStatus", "")
            return status.startswith("pending_")
        return False

    def handle_gate_status_confirmation(self, gate_id, confirmed_status):
        """
        Handle gate status confirmation from SMS response
        
        Args:
            gate_id (str): Gate ID
            confirmed_status (str): Confirmed status from gate ("open" or "closed")
        """
        try:
            logger.info(f"[CONFIRMATION] 📨 Processing status confirmation for gate {gate_id}: {confirmed_status}")
            
            # Get current gate data to check if this matches a pending operation
            current_gate_data = self.firebase.get_gate_data(gate_id)
            if not current_gate_data:
                logger.warning(f"[CONFIRMATION] No gate data found for gate {gate_id}")
                # Still update the status even if no pending operation
                self._update_confirmed_gate_status(gate_id, confirmed_status)
                return
            
            current_status = current_gate_data.get("gateStatus", "")
            operation_status = current_gate_data.get("operationStatus", "")
            awaiting_confirmation = current_gate_data.get("awaitingConfirmation", False)
            expected_status = current_gate_data.get("expectedStatus", "")
            
            logger.info(f"[CONFIRMATION] Current status: {current_status}, Expected: {expected_status}, Awaiting: {awaiting_confirmation}")
            
            # Check if this confirmation matches a pending operation
            if (current_status.startswith("pending_") and 
                operation_status == "in_progress" and 
                awaiting_confirmation and
                expected_status == confirmed_status):
                
                logger.info(f"[CONFIRMATION] ✅ Confirmation matches pending operation!")
                
                # Update gate status to confirmed status
                self._update_confirmed_gate_status(gate_id, confirmed_status, operation_successful=True)
                
                # Remove from pending operations tracking
                if gate_id in self.pending_operations:
                    del self.pending_operations[gate_id]
                    logger.info(f"[CONFIRMATION] Removed gate {gate_id} from pending operations")
            
            elif awaiting_confirmation:
                # Unexpected status - log warning but still update
                logger.warning(f"[CONFIRMATION] ⚠️ Unexpected status confirmation: expected {expected_status}, got {confirmed_status}")
                self._update_confirmed_gate_status(gate_id, confirmed_status, operation_successful=False)
            
            else:
                # No pending operation, just update status
                logger.info(f"[CONFIRMATION] No pending operation, updating status to {confirmed_status}")
                self._update_confirmed_gate_status(gate_id, confirmed_status)
            
        except Exception as e:
            logger.error(f"[CONFIRMATION] Error handling status confirmation for gate {gate_id}: {e}")

    def _update_confirmed_gate_status(self, gate_id, confirmed_status, operation_successful=None):
        """Update gate status with confirmation data"""
        try:
            current_time = datetime.datetime.now().isoformat()
            
            gate_update = {
                "gateStatus": confirmed_status,
                "lastUpdated": current_time,
                "lastConfirmation": current_time,
                "lastConfirmedStatus": confirmed_status,
                "awaitingConfirmation": False,
                "pendingConfirmation": False,
                "confirmationReceived": True,
                "confirmationTime": current_time
            }
            
            if operation_successful is not None:
                if operation_successful:
                    gate_update.update({
                        "operationStatus": "completed",
                        "confirmationResult": "success",
                        "operationCompleted": True
                    })
                    logger.info(f"[CONFIRMATION] ✅ Operation completed successfully for gate {gate_id}")
                else:
                    gate_update.update({
                        "operationStatus": "completed_with_warning",
                        "confirmationResult": "unexpected_status",
                        "operationCompleted": True
                    })
                    logger.warning(f"[CONFIRMATION] ⚠️ Operation completed with unexpected status for gate {gate_id}")
            else:
                gate_update.update({
                    "operationStatus": "completed",
                    "confirmationResult": "status_update"
                })
            
            # Update the gate status in Firebase
            success = self.firebase.update_gate_data(gate_id, gate_update)
            
            if success:
                logger.info(f"[CONFIRMATION] ✅ Successfully updated gate {gate_id} status to '{confirmed_status}'")
                
                # Also handle session management
                self.handle_gate_status_change(gate_id, confirmed_status)
            else:
                logger.error(f"[CONFIRMATION] ❌ Failed to update gate {gate_id} status in database")
            
        except Exception as e:
            logger.error(f"[CONFIRMATION] Error updating confirmed gate status: {e}")











